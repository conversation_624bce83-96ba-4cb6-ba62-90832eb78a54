import{x as p,j as e,Q as j,t as n}from"./app2.js";import{A as v}from"./AdminLayout-CA9t8iTo.js";import{F as y}from"./ArrowLeftIcon-3sOzk9Cr.js";import{F as N}from"./TrashIcon-CkX595jR.js";import{F as w}from"./PlusIcon-CFTIzR_P.js";import"./HomeIcon-BtCkMBaV.js";import"./XMarkIcon-DvlfLOeu.js";import"./EyeIcon-CuARlRRc.js";function B({auth:c,service:s}){const{data:a,setData:i,post:m,processing:d,errors:r}=p({title:s.title||"",description:s.description||"",features:s.features||[""],icon:s.icon||"ScaleIcon",image:null,sort_order:s.sort_order||0,is_active:s.is_active??!0,_method:"PUT"}),x=[{value:"ScaleIcon",label:"⚖️ Scale (Legal)"},{value:"DocumentTextIcon",label:"📄 Document"},{value:"UserGroupIcon",label:"👥 User Group"},{value:"ShieldCheckIcon",label:"🛡️ Shield (Protection)"},{value:"HomeIcon",label:"🏠 Home (Real Estate)"},{value:"HeartIcon",label:"❤️ Heart (Family)"},{value:"BriefcaseIcon",label:"💼 Briefcase (Business)"},{value:"BuildingOfficeIcon",label:"🏢 Building (Corporate)"}],u=()=>{i("features",[...a.features,""])},g=t=>{const o=a.features.filter((l,b)=>b!==t);i("features",o)},f=(t,o)=>{const l=[...a.features];l[t]=o,i("features",l)},h=t=>{t.preventDefault(),m(`/admin/services/${s.id}`,{forceFormData:!0})};return e.jsxs(v,{user:c.user,children:[e.jsx(j,{title:`Edit ${s.title}`}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(n,{href:"/admin/services",className:"mr-4 text-gray-500 hover:text-gray-700",children:e.jsx(y,{className:"h-5 w-5"})}),e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:["Edit Service: ",s.title]})]}),e.jsx("p",{className:"text-gray-600",children:"Update the service information"})]}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:e.jsxs("form",{onSubmit:h,className:"space-y-6 p-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700",children:"Service Title *"}),e.jsx("input",{type:"text",id:"title",value:a.title,onChange:t=>i("title",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"e.g., Legal Consultation"}),r.title&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.title})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description *"}),e.jsx("textarea",{id:"description",rows:4,value:a.description,onChange:t=>i("description",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Describe this service..."}),r.description&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.description})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700",children:"Icon *"}),e.jsx("select",{id:"icon",value:a.icon,onChange:t=>i("icon",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:x.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))}),r.icon&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.icon})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700",children:"Service Image"}),s.image_url&&e.jsxs("div",{className:"mt-2 mb-4",children:[e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Current image:"}),e.jsx("img",{src:`/storage/${s.image_url}`,alt:s.title,className:"w-32 h-24 object-cover rounded-lg border border-gray-300"})]}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:t=>i("image",t.target.files[0]),className:"mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"}),r.image&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.image}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Upload a new image to replace the current one. Recommended size: 800x600px. Max size: 2MB."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Features *"}),a.features.map((t,o)=>e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("input",{type:"text",value:t,onChange:l=>f(o,l.target.value),className:"flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Enter a feature..."}),a.features.length>1&&e.jsx("button",{type:"button",onClick:()=>g(o),className:"text-red-600 hover:text-red-800",children:e.jsx(N,{className:"h-5 w-5"})})]},o)),e.jsxs("button",{type:"button",onClick:u,className:"mt-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(w,{className:"h-4 w-4 mr-1"}),"Add Feature"]}),r.features&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.features})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"sort_order",className:"block text-sm font-medium text-gray-700",children:"Sort Order *"}),e.jsx("input",{type:"number",id:"sort_order",min:"0",value:a.sort_order,onChange:t=>i("sort_order",parseInt(t.target.value)),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"}),r.sort_order&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.sort_order})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"is_active",type:"checkbox",checked:a.is_active,onChange:t=>i("is_active",t.target.checked),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Active (visible on website)"})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[e.jsx(n,{href:"/admin/services",className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:d,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:d?"Updating...":"Update Service"})]})]})})]})})]})}export{B as default};
