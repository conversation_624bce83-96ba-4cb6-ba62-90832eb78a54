[2025-07-17 01:03:33] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#48 {main}
"} 
[2025-07-17 01:03:34] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#21 {main}
"} 
[2025-07-17 01:03:35] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#48 {main}
"} 
[2025-07-17 01:03:35] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#21 {main}
"} 
[2025-07-17 01:05:43] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#48 {main}
"} 
[2025-07-17 01:06:01] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#21 {main}
"} 
[2025-07-17 01:07:13] local.ERROR: Database file at path [C:\Users\<USER>\Desktop\Belgica-Law-Office\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "sessions" where "id" = 4Vrr3bzrc508VMovMfwxtCCVEXPHkaMbbla4oCoP limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"sessions\" where \"id\" = 4Vrr3bzrc508VMovMfwxtCCVEXPHkaMbbla4oCoP limit 1) at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():143}()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#48 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#49 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#50 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#51 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#52 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#53 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getPdo()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():143}()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#51 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#53 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#55 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
[2025-07-17 02:24:01] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email (Connection: sqlite, SQL: insert into "users" ("name", "email", "email_verified_at", "password", "remember_token", "updated_at", "created_at") values (Test User, <EMAIL>, 2025-07-17 02:23:58, $2y$12$Ct1hsc65oTxpsrPw7PzoEuLzUU56Lh4by.udWSE/zcr0ASPgP8VHW, mdVrxdgGx1, 2025-07-17 02:23:59, 2025-07-17 02:23:59)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email (Connection: sqlite, SQL: insert into \"users\" (\"name\", \"email\", \"email_verified_at\", \"password\", \"remember_token\", \"updated_at\", \"created_at\") values (Test User, <EMAIL>, 2025-07-17 02:23:58, $2y$12$Ct1hsc65oTxpsrPw7PzoEuLzUU56Lh4by.udWSE/zcr0ASPgP8VHW, mdVrxdgGx1, 2025-07-17 02:23:59, 2025-07-17 02:23:59)) at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:817)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(350): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Eloquent\\Factories\\Factory->{closure:Illuminate\\Database\\Eloquent\\Factories\\Factory::store():345}()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(345): Illuminate\\Support\\Collection->each()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(301): Illuminate\\Database\\Eloquent\\Factories\\Factory->store()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(295): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\seeders\\DatabaseSeeder.php(18): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():187}()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():70}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#39 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(350): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Eloquent\\Factories\\Factory->{closure:Illuminate\\Database\\Eloquent\\Factories\\Factory::store():345}()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(345): Illuminate\\Support\\Collection->each()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(301): Illuminate\\Database\\Eloquent\\Factories\\Factory->store()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(295): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\seeders\\DatabaseSeeder.php(18): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():187}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():70}()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#41 {main}
"} 
[2025-07-30 05:44:14] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#48 {main}
"} 
[2025-07-30 05:44:36] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#21 {main}
"} 
[2025-07-30 05:44:57] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#48 {main}
"} 
[2025-07-30 05:45:01] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#21 {main}
"} 
[2025-07-31 02:20:00] local.ERROR: PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#20 {main}
"} 
[2025-07-31 02:20:41] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#12 {main}
"} 
[2025-07-31 02:22:08] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: articles.slug (Connection: sqlite, SQL: insert into "articles" ("title", "slug", "excerpt", "content", "category", "tags", "status", "is_featured", "meta_title", "meta_description", "published_at", "updated_at", "created_at") values (Understanding Your Rights in Employment Law, understanding-your-rights-in-employment-law, A comprehensive guide to employee rights and protections under Philippine labor law., <p>Employment law in the Philippines provides extensive protections for workers. Understanding your rights is crucial for maintaining a fair and safe workplace.</p><p>Key areas covered include:</p><ul><li>Working hours and overtime compensation</li><li>Leave entitlements and benefits</li><li>Termination procedures and severance</li><li>Workplace safety and health standards</li></ul><p>If you believe your rights have been violated, it's important to seek legal counsel promptly to protect your interests.</p>, legal-tips, ["employment","labor law","workers rights","Philippines"], published, 1, Employment Rights in the Philippines - Legal Guide, Learn about your employment rights under Philippine labor law. Expert legal guidance on workplace protections and employee benefits., 2025-07-29 02:22:07, 2025-07-31 02:22:07, 2025-07-31 02:22:07)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: articles.slug (Connection: sqlite, SQL: insert into \"articles\" (\"title\", \"slug\", \"excerpt\", \"content\", \"category\", \"tags\", \"status\", \"is_featured\", \"meta_title\", \"meta_description\", \"published_at\", \"updated_at\", \"created_at\") values (Understanding Your Rights in Employment Law, understanding-your-rights-in-employment-law, A comprehensive guide to employee rights and protections under Philippine labor law., <p>Employment law in the Philippines provides extensive protections for workers. Understanding your rights is crucial for maintaining a fair and safe workplace.</p><p>Key areas covered include:</p><ul><li>Working hours and overtime compensation</li><li>Leave entitlements and benefits</li><li>Termination procedures and severance</li><li>Workplace safety and health standards</li></ul><p>If you believe your rights have been violated, it's important to seek legal counsel promptly to protect your interests.</p>, legal-tips, [\"employment\",\"labor law\",\"workers rights\",\"Philippines\"], published, 1, Employment Rights in the Philippines - Legal Guide, Learn about your employment rights under Philippine labor law. Expert legal guidance on workplace protections and employee benefits., 2025-07-29 02:22:07, 2025-07-31 02:22:07, 2025-07-31 02:22:07)) at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:817)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1188}()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\seeders\\ArticleSeeder.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\ArticleSeeder->run()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():187}()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():70}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#40 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: articles.slug at C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1188}()
#13 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap()
#14 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#15 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#16 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#17 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\database\\seeders\\ArticleSeeder.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\ArticleSeeder->run()
#19 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#20 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#24 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():187}()
#25 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#26 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():70}()
#27 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#28 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#30 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#31 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#32 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#33 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#34 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#35 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#36 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#37 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#38 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#39 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#40 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#41 C:\\Users\\<USER>\\Desktop\\Belgica-Law-Office\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#42 {main}
"} 
