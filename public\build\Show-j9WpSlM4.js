import{j as e,Q as n,t as i,S as m}from"./app2.js";import{A as c}from"./AdminLayout-CA9t8iTo.js";import{F as x}from"./ArrowLeftIcon-3sOzk9Cr.js";import{F as o}from"./PencilIcon-BlseKOW2.js";import{F as h}from"./TrashIcon-CkX595jR.js";import{F as g}from"./CheckCircleIcon-C2jnGy7x.js";import{F as u}from"./XCircleIcon-Bp7te63t.js";import"./HomeIcon-BtCkMBaV.js";import"./XMarkIcon-DvlfLOeu.js";import"./EyeIcon-CuARlRRc.js";function S({auth:d,service:s}){const l=()=>{confirm(`Are you sure you want to delete "${s.title}"?`)&&m.delete(`/admin/services/${s.id}`)},r=t=>({ScaleIcon:"⚖️",DocumentTextIcon:"📄",UserGroupIcon:"👥",ShieldCheckIcon:"🛡️",HomeIcon:"🏠",HeartIcon:"❤️",BriefcaseIcon:"💼",BuildingOfficeIcon:"🏢"})[t]||"📋";return e.jsxs(c,{user:d.user,children:[e.jsx(n,{title:`View ${s.title}`}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(i,{href:"/admin/services",className:"mr-4 text-gray-500 hover:text-gray-700",children:e.jsx(x,{className:"h-5 w-5"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:s.title})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(i,{href:`/admin/services/${s.id}/edit`,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(o,{className:"-ml-1 mr-2 h-4 w-4"}),"Edit"]}),e.jsxs("button",{onClick:l,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[e.jsx(h,{className:"-ml-1 mr-2 h-4 w-4"}),"Delete"]})]})]})}),e.jsx("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:e.jsxs("div",{className:"px-6 py-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"text-4xl mr-4",children:r(s.icon)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.title}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex items-center",children:s.is_active?e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"h-5 w-5 text-green-500 mr-1"}),e.jsx("span",{className:"text-sm text-green-700 font-medium",children:"Active"})]}):e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"h-5 w-5 text-red-500 mr-1"}),e.jsx("span",{className:"text-sm text-red-700 font-medium",children:"Inactive"})]})}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Sort Order: ",s.sort_order]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Description"}),e.jsx("p",{className:"text-gray-700 leading-relaxed",children:s.description})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Features"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:s.features.map((t,a)=>e.jsxs("div",{className:"flex items-center p-3 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mr-3"}),e.jsx("span",{className:"text-blue-900 font-medium",children:t})]},a))})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Service Information"}),e.jsxs("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Created"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:new Date(s.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),e.jsxs("div",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:new Date(s.updated_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),e.jsxs("div",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Icon"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:s.icon})]}),e.jsxs("div",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Status"}),e.jsx("dd",{className:"mt-1",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.is_active?"Active":"Inactive"})})]})]})]})]})}),e.jsxs("div",{className:"mt-8 bg-white shadow rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-gray-200",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Website Preview"}),e.jsx("p",{className:"text-sm text-gray-500",children:"How this service appears on your website"})]}),e.jsx("div",{className:"px-6 py-8 bg-gray-50",children:e.jsx("div",{className:"max-w-sm mx-auto bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-lg mb-4",children:e.jsx("span",{className:"text-2xl",children:r(s.icon)})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:s.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:s.description}),e.jsxs("ul",{className:"space-y-2",children:[s.features.slice(0,3).map((t,a)=>e.jsxs("li",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("div",{className:"w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"}),t]},a)),s.features.length>3&&e.jsxs("li",{className:"text-sm text-gray-500",children:["+",s.features.length-3," more features"]})]})]})})})]})]})})]})}export{S as default};
