import{j as e,Q as x,t as r,S as o}from"./app2.js";import{A as h}from"./AdminLayout-CA9t8iTo.js";import{F as l}from"./PlusIcon-CFTIzR_P.js";import{F as f}from"./CheckCircleIcon-C2jnGy7x.js";import{F as g}from"./XCircleIcon-Bp7te63t.js";import{F as u}from"./EyeIcon-CuARlRRc.js";import{F as j}from"./PencilIcon-BlseKOW2.js";import{F as p}from"./TrashIcon-CkX595jR.js";import"./HomeIcon-BtCkMBaV.js";import"./XMarkIcon-DvlfLOeu.js";function R({auth:d,services:i,flash:t}){const m=s=>{confirm(`Are you sure you want to delete "${s.title}"?`)&&o.delete(`/admin/services/${s.id}`)},n=s=>({ScaleIcon:"⚖️",DocumentTextIcon:"📄",UserGroupIcon:"👥",ShieldCheckIcon:"🛡️",HomeIcon:"🏠",HeartIcon:"❤️",BriefcaseIcon:"💼",BuildingOfficeIcon:"🏢"})[s]||"📋";return e.jsxs(h,{user:d.user,children:[e.jsx(x,{title:"Manage Services"}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"md:flex md:items-center md:justify-between mb-6",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Manage Services"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Add, edit, and manage your legal services"})]}),e.jsx("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:e.jsxs(r,{href:"/admin/services/create",className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(l,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Service"]})})]}),(t==null?void 0:t.success)&&e.jsx("div",{className:"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:t.success}),e.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:i.map(s=>e.jsxs("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[s.image_url&&e.jsx("div",{className:"h-48 overflow-hidden",children:e.jsx("img",{src:`/storage/${s.image_url}`,alt:s.title,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"text-2xl",children:n(s.icon)})}),e.jsxs("div",{className:"ml-4 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:s.title}),e.jsx("div",{className:"flex items-center",children:s.is_active?e.jsx(f,{className:"h-5 w-5 text-green-500",title:"Active"}):e.jsx(g,{className:"h-5 w-5 text-red-500",title:"Inactive"})})]}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["Sort Order: ",s.sort_order]})]})]}),e.jsx("div",{className:"mt-4",children:e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:s.description})}),e.jsxs("div",{className:"mt-4",children:[e.jsx("div",{className:"text-xs text-gray-500 mb-2",children:"Features:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.features.slice(0,3).map((a,c)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:a},c)),s.features.length>3&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:["+",s.features.length-3," more"]})]})]})]}),e.jsx("div",{className:"bg-gray-50 px-6 py-3",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(r,{href:`/admin/services/${s.id}`,className:"text-indigo-600 hover:text-indigo-900 text-sm font-medium flex items-center",children:[e.jsx(u,{className:"h-4 w-4 mr-1"}),"View"]}),e.jsxs(r,{href:`/admin/services/${s.id}/edit`,className:"text-indigo-600 hover:text-indigo-900 text-sm font-medium flex items-center",children:[e.jsx(j,{className:"h-4 w-4 mr-1"}),"Edit"]})]}),e.jsxs("button",{onClick:()=>m(s),className:"text-red-600 hover:text-red-900 text-sm font-medium flex items-center",children:[e.jsx(p,{className:"h-4 w-4 mr-1"}),"Delete"]})]})})]},s.id))}),i.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 text-6xl mb-4",children:"📋"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No services yet"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Get started by creating your first service."}),e.jsxs(r,{href:"/admin/services/create",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700",children:[e.jsx(l,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Your First Service"]})]})]})})]})}export{R as default};
