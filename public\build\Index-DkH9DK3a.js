import{r as h,j as e,Q as b,t as i,S as x}from"./app2.js";import{A as w}from"./AdminLayout-CA9t8iTo.js";import{F as n,a as k}from"./XMarkIcon-DvlfLOeu.js";import{F as c}from"./EyeIcon-CuARlRRc.js";import{F as S}from"./CheckCircleIcon-C2jnGy7x.js";import{F}from"./MagnifyingGlassIcon--WN8JX24.js";import{F as $}from"./TrashIcon-CkX595jR.js";import{F as R}from"./ClockIcon-D-sImXRf.js";import"./HomeIcon-BtCkMBaV.js";function E({auth:g,contacts:r,filters:t,stats:a,flash:l}){const[m,u]=h.useState(t.search||""),[o,j]=h.useState(t.status||"all"),f=s=>{s.preventDefault(),x.get("/admin/contacts",{search:m,status:o},{preserveState:!0})},N=s=>{j(s),x.get("/admin/contacts",{search:m,status:s},{preserveState:!0})},p=s=>{confirm(`Are you sure you want to delete the contact from ${s.name}?`)&&x.delete(`/admin/contacts/${s.id}`)},v=s=>({unread:"bg-red-100 text-red-800",read:"bg-yellow-100 text-yellow-800",replied:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",y=s=>{switch(s){case"unread":return e.jsx(n,{className:"h-4 w-4"});case"read":return e.jsx(c,{className:"h-4 w-4"});case"replied":return e.jsx(k,{className:"h-4 w-4"});default:return e.jsx(R,{className:"h-4 w-4"})}};return e.jsxs(w,{user:g.user,children:[e.jsx(b,{title:"Manage Contacts"}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"md:flex md:items-center md:justify-between mb-6",children:e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Contact Messages"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Manage contact form submissions from your website"})]})}),(l==null?void 0:l.success)&&e.jsx("div",{className:"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:l.success}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(n,{className:"h-6 w-6 text-gray-400"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:a.total})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(n,{className:"h-6 w-6 text-red-400"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Unread"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:a.unread})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(c,{className:"h-6 w-6 text-yellow-400"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Read"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:a.read})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(S,{className:"h-6 w-6 text-green-400"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Replied"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:a.replied})]})})]})})})]}),e.jsx("div",{className:"bg-white shadow rounded-lg mb-6",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("form",{onSubmit:f,className:"flex",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(F,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:"text",value:m,onChange:s=>u(s.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Search contacts..."})]}),e.jsx("button",{type:"submit",className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Search"})]}),e.jsx("div",{className:"flex space-x-2",children:[{key:"all",label:"All"},{key:"unread",label:"Unread"},{key:"read",label:"Read"},{key:"replied",label:"Replied"}].map(s=>e.jsx("button",{onClick:()=>N(s.key),className:`px-3 py-2 text-sm font-medium rounded-md ${o===s.key?"bg-indigo-100 text-indigo-700":"text-gray-500 hover:text-gray-700"}`,children:s.label},s.key))})]})})}),e.jsxs("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[e.jsx("ul",{className:"divide-y divide-gray-200",children:r.data.length>0?r.data.map(s=>e.jsx("li",{children:e.jsxs("div",{className:"px-4 py-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center flex-1",children:[e.jsx("div",{className:"flex-shrink-0 mr-4",children:y(s.status)}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-indigo-600 truncate",children:s.name}),e.jsx("div",{className:"ml-2 flex-shrink-0 flex",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${v(s.status)}`,children:s.status})})]}),e.jsxs("div",{className:"mt-2",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[e.jsx("p",{className:"truncate",children:s.email}),s.phone&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"mx-2",children:"•"}),e.jsx("p",{className:"truncate",children:s.phone})]})]}),e.jsx("p",{className:"text-sm text-gray-900 font-medium mt-1",children:s.subject}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1 line-clamp-2",children:[s.message.substring(0,150),"..."]}),e.jsx("p",{className:"text-xs text-gray-400 mt-2",children:new Date(s.created_at).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(i,{href:`/admin/contacts/${s.id}`,className:"text-indigo-600 hover:text-indigo-900 text-sm font-medium flex items-center",children:[e.jsx(c,{className:"h-4 w-4 mr-1"}),"View"]}),e.jsxs("button",{onClick:()=>p(s),className:"text-red-600 hover:text-red-900 text-sm font-medium flex items-center",children:[e.jsx($,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})},s.id)):e.jsxs("li",{className:"px-4 py-12 text-center",children:[e.jsx(n,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No contacts"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:t.search||t.status!=="all"?"No contacts match your current filters.":"No contact messages have been received yet."})]})}),r.links&&r.links.length>3&&e.jsx("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[r.prev_page_url&&e.jsx(i,{href:r.prev_page_url,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Previous"}),r.next_page_url&&e.jsx(i,{href:r.next_page_url,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Next"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700",children:["Showing ",e.jsx("span",{className:"font-medium",children:r.from})," to"," ",e.jsx("span",{className:"font-medium",children:r.to})," of"," ",e.jsx("span",{className:"font-medium",children:r.total})," results"]})}),e.jsx("div",{children:e.jsx("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:r.links.map((s,d)=>e.jsx(i,{href:s.url||"#",className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${s.active?"z-10 bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"} ${d===0?"rounded-l-md":""} ${d===r.links.length-1?"rounded-r-md":""}`,dangerouslySetInnerHTML:{__html:s.label}},d))})})]})]})})]})]})})]})}export{E as default};
