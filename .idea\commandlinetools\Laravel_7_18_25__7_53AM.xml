<?xml version="1.0" encoding="UTF-8"?>
<framework xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="schemas/frameworkDescriptionVersion1.1.4.xsd" frameworkId="com.laravel.component" name="Laravel_7/18/25, 7:53 AM" invoke="f38e05d1-46ed-47b9-9664-e84659eb2d55 C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan" alias="artisan" enabled="true" version="2">
  <extraData><![CDATA[version:3]]></extraData>
  <command>
    <name>_complete</name>
    <help><![CDATA[Internal command to provide shell completion suggestions<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--shell</td><td>(-s)</td><td>The shell type ("bash", "fish", "zsh")</td></tr> <tr><td>--input</td><td>(-i)</td><td>An array of input tokens (e.g. COMP_WORDS or argv)</td></tr> <tr><td>--current</td><td>(-c)</td><td>The index of the "input" array that the cursor is in (e.g. COMP_CWORD)</td></tr> <tr><td>--api-version</td><td>(-a)</td><td>The API version of the completion script</td></tr> <tr><td>--symfony</td><td>(-S)</td><td>deprecated</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--shell" shortcut="-s" pattern="equals">
        <help><![CDATA[The shell type ("bash", "fish", "zsh")]]></help>
      </option>
      <option name="--input" shortcut="-i" pattern="equals">
        <help><![CDATA[An array of input tokens (e.g. COMP_WORDS or argv)]]></help>
      </option>
      <option name="--current" shortcut="-c" pattern="equals">
        <help><![CDATA[The index of the "input" array that the cursor is in (e.g. COMP_CWORD)]]></help>
      </option>
      <option name="--api-version" shortcut="-a" pattern="equals">
        <help><![CDATA[The API version of the completion script]]></help>
      </option>
      <option name="--symfony" shortcut="-S" pattern="equals">
        <help><![CDATA[deprecated]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>about</name>
    <help><![CDATA[Display basic information about your application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--only</td><td></td><td>The section to display</td></tr> <tr><td>--json</td><td></td><td>Output the information as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--only" shortcut="" pattern="equals">
        <help><![CDATA[The section to display]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the information as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>clear-compiled</name>
    <help><![CDATA[Remove the compiled class file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>completion</name>
    <help><![CDATA[The <b>completion</> command dumps the shell completion script required<br> to use shell autocompletion (currently, bash, fish, zsh completion are supported).<br> <br> <comment>Static installation<br> -------------------</><br> <br> Dump the script to a global completion file and restart your shell:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan completion | sudo tee /etc/bash_completion.d/artisan</><br> <br> Or dump the script to a local file and source it:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan completion > completion.sh</><br> <br> <comment># source the file whenever you use the project</><br> <b>source completion.sh</><br> <br> <comment># or add this line at the end of your "~/.bashrc" file:</><br> <b>source /path/to/completion.sh</><br> <br> <comment>Dynamic installation<br> --------------------</><br> <br> Add this to the end of your shell configuration file (e.g. <b>"~/.bashrc"</>):<br> <br> <b>eval "$(C:\Users\<USER>\Desktop\Belgica-Law-Office\artisan completion )"</><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--debug</td><td></td><td>Tail the completion debug log</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>shell[=null]</params>
    <optionsBefore>
      <option name="--debug" shortcut="">
        <help><![CDATA[Tail the completion debug log]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db</name>
    <help><![CDATA[Start a new database CLI session<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--read</td><td></td><td>Connect to the read connection</td></tr> <tr><td>--write</td><td></td><td>Connect to the write connection</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--read" shortcut="">
        <help><![CDATA[Connect to the read connection]]></help>
      </option>
      <option name="--write" shortcut="">
        <help><![CDATA[Connect to the write connection]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>docs</name>
    <help><![CDATA[If you would like to perform a content search against the documentation, you may call: <fg=green>php artisan docs -- </><fg=green;options=bold;>search query here</><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>page[=null] section[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>down</name>
    <help><![CDATA[Put the application into maintenance / demo mode<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--redirect</td><td></td><td>The path that users should be redirected to</td></tr> <tr><td>--render</td><td></td><td>The view that should be prerendered for display during maintenance mode</td></tr> <tr><td>--retry</td><td></td><td>The number of seconds after which the request may be retried</td></tr> <tr><td>--refresh</td><td></td><td>The number of seconds after which the browser may refresh</td></tr> <tr><td>--secret</td><td></td><td>The secret phrase that may be used to bypass maintenance mode</td></tr> <tr><td>--with-secret</td><td></td><td>Generate a random secret phrase that may be used to bypass maintenance mode</td></tr> <tr><td>--status</td><td></td><td>The status code that should be used when returning the maintenance mode response</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--redirect" shortcut="" pattern="equals">
        <help><![CDATA[The path that users should be redirected to]]></help>
      </option>
      <option name="--render" shortcut="" pattern="equals">
        <help><![CDATA[The view that should be prerendered for display during maintenance mode]]></help>
      </option>
      <option name="--retry" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds after which the request may be retried]]></help>
      </option>
      <option name="--refresh" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds after which the browser may refresh]]></help>
      </option>
      <option name="--secret" shortcut="" pattern="equals">
        <help><![CDATA[The secret phrase that may be used to bypass maintenance mode]]></help>
      </option>
      <option name="--with-secret" shortcut="">
        <help><![CDATA[Generate a random secret phrase that may be used to bypass maintenance mode]]></help>
      </option>
      <option name="--status" shortcut="" pattern="equals">
        <help><![CDATA[The status code that should be used when returning the maintenance mode response]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>env</name>
    <help><![CDATA[Display the current framework environment<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>help</name>
    <help><![CDATA[The <b>help</b> command displays help for a given command:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan help list</b><br> <br> You can also output the help in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan help --format=xml list</b><br> <br> To display the list of available commands, please use the <b>list</b> command.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--raw</td><td></td><td>To output raw command help</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>command_name[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command help]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>inspire</name>
    <help><![CDATA[Display an inspiring quote<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>invoke-serialized-closure</name>
    <help><![CDATA[Invoke the given serialized closure<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>code[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>list</name>
    <help><![CDATA[The <b>list</b> command lists all commands:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan list</b><br> <br> You can also display the commands for a specific namespace:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan list test</b><br> <br> You can also output the information in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan list --format=xml</b><br> <br> It's also possible to get raw list of commands (useful for embedding command runner):<br> <br> <b>C:/Users/<USER>/Desktop/Belgica-Law-Office/artisan list --raw</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--raw</td><td></td><td>To output raw command list</td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--short</td><td></td><td>To skip describing commands' arguments</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>namespace[=null]</params>
    <optionsBefore>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command list]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--short" shortcut="">
        <help><![CDATA[To skip describing commands' arguments]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate</name>
    <help><![CDATA[Run the database migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--schema-path</td><td></td><td>The path to a schema dump file</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--seeder</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--step</td><td></td><td>Force the migrations to be run so they can be rolled back individually</td></tr> <tr><td>--graceful</td><td></td><td>Return a successful exit code even if an error occurs</td></tr> <tr><td>--isolated</td><td></td><td>Do not run the command if another instance of the command is already running</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--schema-path" shortcut="" pattern="equals">
        <help><![CDATA[The path to a schema dump file]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--seeder" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--step" shortcut="">
        <help><![CDATA[Force the migrations to be run so they can be rolled back individually]]></help>
      </option>
      <option name="--graceful" shortcut="">
        <help><![CDATA[Return a successful exit code even if an error occurs]]></help>
      </option>
      <option name="--isolated" shortcut="" pattern="equals">
        <help><![CDATA[Do not run the command if another instance of the command is already running]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>optimize</name>
    <help><![CDATA[Cache framework bootstrap, configuration, and metadata to increase performance<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--except</td><td>(-e)</td><td>Do not run the commands matching the key or name</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--except" shortcut="-e" pattern="equals">
        <help><![CDATA[Do not run the commands matching the key or name]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>pail</name>
    <help><![CDATA[Tails the application logs.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--filter</td><td></td><td>Filter the logs by the given value</td></tr> <tr><td>--message</td><td></td><td>Filter the logs by the given message</td></tr> <tr><td>--level</td><td></td><td>Filter the logs by the given level</td></tr> <tr><td>--auth</td><td></td><td>Filter the logs by the given authenticated ID</td></tr> <tr><td>--user</td><td></td><td>Filter the logs by the given authenticated ID (alias for --auth)</td></tr> <tr><td>--timeout</td><td></td><td>The maximum execution time in seconds</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--filter" shortcut="" pattern="equals">
        <help><![CDATA[Filter the logs by the given value]]></help>
      </option>
      <option name="--message" shortcut="" pattern="equals">
        <help><![CDATA[Filter the logs by the given message]]></help>
      </option>
      <option name="--level" shortcut="" pattern="equals">
        <help><![CDATA[Filter the logs by the given level]]></help>
      </option>
      <option name="--auth" shortcut="" pattern="equals">
        <help><![CDATA[Filter the logs by the given authenticated ID]]></help>
      </option>
      <option name="--user" shortcut="" pattern="equals">
        <help><![CDATA[Filter the logs by the given authenticated ID (alias for --auth)]]></help>
      </option>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[The maximum execution time in seconds]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>serve</name>
    <help><![CDATA[Serve the application on the PHP development server<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--host</td><td></td><td>The host address to serve the application on</td></tr> <tr><td>--port</td><td></td><td>The port to serve the application on</td></tr> <tr><td>--tries</td><td></td><td>The max number of ports to attempt to serve from</td></tr> <tr><td>--no-reload</td><td></td><td>Do not reload the development server on .env file changes</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--host" shortcut="" pattern="equals">
        <help><![CDATA[The host address to serve the application on]]></help>
      </option>
      <option name="--port" shortcut="" pattern="equals">
        <help><![CDATA[The port to serve the application on]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[The max number of ports to attempt to serve from]]></help>
      </option>
      <option name="--no-reload" shortcut="">
        <help><![CDATA[Do not reload the development server on .env file changes]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>test</name>
    <help><![CDATA[Run the application tests<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--without-tty</td><td></td><td>Disable output to TTY</td></tr> <tr><td>--compact</td><td></td><td>Indicates whether the compact printer should be used</td></tr> <tr><td>--coverage</td><td></td><td>Indicates whether code coverage information should be collected</td></tr> <tr><td>--min</td><td></td><td>Indicates the minimum threshold enforcement for code coverage</td></tr> <tr><td>--parallel</td><td>(-p)</td><td>Indicates if the tests should run in parallel</td></tr> <tr><td>--profile</td><td></td><td>Lists top 10 slowest tests</td></tr> <tr><td>--recreate-databases</td><td></td><td>Indicates if the test databases should be re-created</td></tr> <tr><td>--drop-databases</td><td></td><td>Indicates if the test databases should be dropped</td></tr> <tr><td>--without-databases</td><td></td><td>Indicates if database configuration should be performed</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--without-tty" shortcut="">
        <help><![CDATA[Disable output to TTY]]></help>
      </option>
      <option name="--compact" shortcut="">
        <help><![CDATA[Indicates whether the compact printer should be used]]></help>
      </option>
      <option name="--coverage" shortcut="">
        <help><![CDATA[Indicates whether code coverage information should be collected]]></help>
      </option>
      <option name="--min" shortcut="" pattern="equals">
        <help><![CDATA[Indicates the minimum threshold enforcement for code coverage]]></help>
      </option>
      <option name="--parallel" shortcut="-p">
        <help><![CDATA[Indicates if the tests should run in parallel]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Lists top 10 slowest tests]]></help>
      </option>
      <option name="--recreate-databases" shortcut="">
        <help><![CDATA[Indicates if the test databases should be re-created]]></help>
      </option>
      <option name="--drop-databases" shortcut="">
        <help><![CDATA[Indicates if the test databases should be dropped]]></help>
      </option>
      <option name="--without-databases" shortcut="">
        <help><![CDATA[Indicates if database configuration should be performed]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>tinker</name>
    <help><![CDATA[Interact with your application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--execute</td><td></td><td>Execute the given code using Tinker</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>include[=null]</params>
    <optionsBefore>
      <option name="--execute" shortcut="" pattern="equals">
        <help><![CDATA[Execute the given code using Tinker]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>up</name>
    <help><![CDATA[Bring the application out of maintenance mode<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>auth:clear-resets</name>
    <help><![CDATA[Flush expired password reset tokens<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>breeze:install</name>
    <help><![CDATA[Install the Breeze controllers and resources<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dark</td><td></td><td>Indicate that dark mode support should be installed</td></tr> <tr><td>--pest</td><td></td><td>Indicate that Pest should be installed</td></tr> <tr><td>--ssr</td><td></td><td>Indicates if Inertia SSR support should be installed</td></tr> <tr><td>--typescript</td><td></td><td>Indicates if TypeScript is preferred for the Inertia stack</td></tr> <tr><td>--eslint</td><td></td><td>Indicates if ESLint with Prettier should be installed</td></tr> <tr><td>--composer</td><td></td><td>Absolute path to the Composer binary which should be used to install packages</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>stack</params>
    <optionsBefore>
      <option name="--dark" shortcut="">
        <help><![CDATA[Indicate that dark mode support should be installed]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Indicate that Pest should be installed]]></help>
      </option>
      <option name="--ssr" shortcut="">
        <help><![CDATA[Indicates if Inertia SSR support should be installed]]></help>
      </option>
      <option name="--typescript" shortcut="">
        <help><![CDATA[Indicates if TypeScript is preferred for the Inertia stack]]></help>
      </option>
      <option name="--eslint" shortcut="">
        <help><![CDATA[Indicates if ESLint with Prettier should be installed]]></help>
      </option>
      <option name="--composer" shortcut="" pattern="equals">
        <help><![CDATA[Absolute path to the Composer binary which should be used to install packages]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:clear</name>
    <help><![CDATA[Flush the application cache<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--tags</td><td></td><td>The cache tags you would like to clear</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>store[=null]</params>
    <optionsBefore>
      <option name="--tags" shortcut="" pattern="equals">
        <help><![CDATA[The cache tags you would like to clear]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:forget</name>
    <help><![CDATA[Remove an item from the cache<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>key store[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:prune-stale-tags</name>
    <help><![CDATA[Prune stale cache tags from the cache (Redis only)<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>store[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>channel:list</name>
    <help><![CDATA[List all registered private broadcast channels<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:cache</name>
    <help><![CDATA[Create a cache file for faster configuration loading<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:clear</name>
    <help><![CDATA[Remove the configuration cache file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:publish</name>
    <help><![CDATA[Publish configuration files to your application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td></td><td>Publish all configuration files</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing configuration files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--all" shortcut="">
        <help><![CDATA[Publish all configuration files]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing configuration files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:show</name>
    <help><![CDATA[Display all of the values for a given configuration file or key<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>config</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:monitor</name>
    <help><![CDATA[Monitor the number of connections on the specified database<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--databases</td><td></td><td>The database connections to monitor</td></tr> <tr><td>--max</td><td></td><td>The maximum number of connections that can be open before an event is dispatched</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--databases" shortcut="" pattern="equals">
        <help><![CDATA[The database connections to monitor]]></help>
      </option>
      <option name="--max" shortcut="" pattern="equals">
        <help><![CDATA[The maximum number of connections that can be open before an event is dispatched]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:seed</name>
    <help><![CDATA[Seed the database with records<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--class</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--database</td><td></td><td>The database connection to seed</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>class[=null]</params>
    <optionsBefore>
      <option name="--class" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to seed]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:show</name>
    <help><![CDATA[Display information about the given database<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection</td></tr> <tr><td>--json</td><td></td><td>Output the database information as JSON</td></tr> <tr><td>--counts</td><td></td><td>Show the table row count <bg=red;options=bold> Note: This can be slow on large databases </></td></tr> <tr><td>--views</td><td></td><td>Show the database views <bg=red;options=bold> Note: This can be slow on large databases </></td></tr> <tr><td>--types</td><td></td><td>Show the user defined types</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the database information as JSON]]></help>
      </option>
      <option name="--counts" shortcut="">
        <help><![CDATA[Show the table row count <bg=red;options=bold> Note: This can be slow on large databases </>]]></help>
      </option>
      <option name="--views" shortcut="">
        <help><![CDATA[Show the database views <bg=red;options=bold> Note: This can be slow on large databases </>]]></help>
      </option>
      <option name="--types" shortcut="">
        <help><![CDATA[Show the user defined types]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:table</name>
    <help><![CDATA[Display information about the given database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection</td></tr> <tr><td>--json</td><td></td><td>Output the table information as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>table[=null]</params>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the table information as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:wipe</name>
    <help><![CDATA[Drop all tables, views, and types<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--drop-views</td><td></td><td>Drop all tables and views</td></tr> <tr><td>--drop-types</td><td></td><td>Drop all tables and types (Postgres only)</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--drop-views" shortcut="">
        <help><![CDATA[Drop all tables and views]]></help>
      </option>
      <option name="--drop-types" shortcut="">
        <help><![CDATA[Drop all tables and types (Postgres only)]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>env:decrypt</name>
    <help><![CDATA[Decrypt an environment file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--key</td><td></td><td>The encryption key</td></tr> <tr><td>--cipher</td><td></td><td>The encryption cipher</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> <tr><td>--force</td><td></td><td>Overwrite the existing environment file</td></tr> <tr><td>--path</td><td></td><td>Path to write the decrypted file</td></tr> <tr><td>--filename</td><td></td><td>Filename of the decrypted file</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--key" shortcut="" pattern="equals">
        <help><![CDATA[The encryption key]]></help>
      </option>
      <option name="--cipher" shortcut="" pattern="equals">
        <help><![CDATA[The encryption cipher]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite the existing environment file]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[Path to write the decrypted file]]></help>
      </option>
      <option name="--filename" shortcut="" pattern="equals">
        <help><![CDATA[Filename of the decrypted file]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>env:encrypt</name>
    <help><![CDATA[Encrypt an environment file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--key</td><td></td><td>The encryption key</td></tr> <tr><td>--cipher</td><td></td><td>The encryption cipher</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> <tr><td>--prune</td><td></td><td>Delete the original environment file</td></tr> <tr><td>--force</td><td></td><td>Overwrite the existing encrypted environment file</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--key" shortcut="" pattern="equals">
        <help><![CDATA[The encryption key]]></help>
      </option>
      <option name="--cipher" shortcut="" pattern="equals">
        <help><![CDATA[The encryption cipher]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
      <option name="--prune" shortcut="">
        <help><![CDATA[Delete the original environment file]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite the existing encrypted environment file]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:cache</name>
    <help><![CDATA[Discover and cache the application's events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:clear</name>
    <help><![CDATA[Clear all cached events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:generate</name>
    <help><![CDATA[Generate the missing events and listeners based on registration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:list</name>
    <help><![CDATA[List the application's events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--event</td><td></td><td>Filter the events by name</td></tr> <tr><td>--json</td><td></td><td>Output the events and listeners as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--event" shortcut="" pattern="equals">
        <help><![CDATA[Filter the events by name]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the events and listeners as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>inertia:middleware</name>
    <help><![CDATA[Create a new Inertia middleware<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Create the class even if the Middleware already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the Middleware already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>inertia:start-ssr</name>
    <help><![CDATA[Start the Inertia SSR server<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--runtime</td><td></td><td>The runtime to use (`node` or `bun`)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--runtime" shortcut="" pattern="equals">
        <help><![CDATA[The runtime to use (`node` or `bun`)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>inertia:stop-ssr</name>
    <help><![CDATA[Stop the Inertia SSR server<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>install:api</name>
    <help><![CDATA[Create an API routes file and install Laravel Sanctum or Laravel Passport<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--composer</td><td></td><td>Absolute path to the Composer binary which should be used to install packages</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing API routes file</td></tr> <tr><td>--passport</td><td></td><td>Install Laravel Passport instead of Laravel Sanctum</td></tr> <tr><td>--without-migration-prompt</td><td></td><td>Do not prompt to run pending migrations</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--composer" shortcut="" pattern="equals">
        <help><![CDATA[Absolute path to the Composer binary which should be used to install packages]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing API routes file]]></help>
      </option>
      <option name="--passport" shortcut="">
        <help><![CDATA[Install Laravel Passport instead of Laravel Sanctum]]></help>
      </option>
      <option name="--without-migration-prompt" shortcut="">
        <help><![CDATA[Do not prompt to run pending migrations]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>install:broadcasting</name>
    <help><![CDATA[Create a broadcasting channel routes file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--composer</td><td></td><td>Absolute path to the Composer binary which should be used to install packages</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing broadcasting routes file</td></tr> <tr><td>--without-reverb</td><td></td><td>Do not prompt to install Laravel Reverb</td></tr> <tr><td>--reverb</td><td></td><td>Install Laravel Reverb as the default broadcaster</td></tr> <tr><td>--pusher</td><td></td><td>Install Pusher as the default broadcaster</td></tr> <tr><td>--ably</td><td></td><td>Install Ably as the default broadcaster</td></tr> <tr><td>--without-node</td><td></td><td>Do not prompt to install Node dependencies</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--composer" shortcut="" pattern="equals">
        <help><![CDATA[Absolute path to the Composer binary which should be used to install packages]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing broadcasting routes file]]></help>
      </option>
      <option name="--without-reverb" shortcut="">
        <help><![CDATA[Do not prompt to install Laravel Reverb]]></help>
      </option>
      <option name="--reverb" shortcut="">
        <help><![CDATA[Install Laravel Reverb as the default broadcaster]]></help>
      </option>
      <option name="--pusher" shortcut="">
        <help><![CDATA[Install Pusher as the default broadcaster]]></help>
      </option>
      <option name="--ably" shortcut="">
        <help><![CDATA[Install Ably as the default broadcaster]]></help>
      </option>
      <option name="--without-node" shortcut="">
        <help><![CDATA[Do not prompt to install Node dependencies]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>key:generate</name>
    <help><![CDATA[Set the application key<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--show</td><td></td><td>Display the key instead of modifying files</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--show" shortcut="">
        <help><![CDATA[Display the key instead of modifying files]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>lang:publish</name>
    <help><![CDATA[Publish all language files that are available for customization<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--existing</td><td></td><td>Publish and overwrite only the files that have already been published</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--existing" shortcut="">
        <help><![CDATA[Publish and overwrite only the files that have already been published]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:cache-table</name>
    <help><![CDATA[Create a migration for the cache database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:cast</name>
    <help><![CDATA[Create a new custom Eloquent cast class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the cast already exists</td></tr> <tr><td>--inbound</td><td></td><td>Generate an inbound cast class</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the cast already exists]]></help>
      </option>
      <option name="--inbound" shortcut="">
        <help><![CDATA[Generate an inbound cast class]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:channel</name>
    <help><![CDATA[Create a new channel class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the channel already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the channel already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:class</name>
    <help><![CDATA[Create a new class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--invokable</td><td>(-i)</td><td>Generate a single method, invokable class</td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the class already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--invokable" shortcut="-i">
        <help><![CDATA[Generate a single method, invokable class]]></help>
      </option>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the class already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:command</name>
    <help><![CDATA[Create a new Artisan command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the console command already exists</td></tr> <tr><td>--command</td><td></td><td>The terminal command that will be used to invoke the class</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Console command</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Console command</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Console command</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the console command already exists]]></help>
      </option>
      <option name="--command" shortcut="" pattern="equals">
        <help><![CDATA[The terminal command that will be used to invoke the class]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Console command]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Console command]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Console command]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:component</name>
    <help><![CDATA[Create a new view component class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--inline</td><td></td><td>Create a component that renders an inline view</td></tr> <tr><td>--view</td><td></td><td>Create an anonymous component with only a view</td></tr> <tr><td>--path</td><td></td><td>The location where the component view should be created</td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the component already exists</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Component</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Component</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Component</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--inline" shortcut="">
        <help><![CDATA[Create a component that renders an inline view]]></help>
      </option>
      <option name="--view" shortcut="">
        <help><![CDATA[Create an anonymous component with only a view]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The location where the component view should be created]]></help>
      </option>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the component already exists]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Component]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Component]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Component]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:controller</name>
    <help><![CDATA[Create a new controller class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--api</td><td></td><td>Exclude the create and edit methods from the controller</td></tr> <tr><td>--type</td><td></td><td>Manually specify the controller stub file to use</td></tr> <tr><td>--force</td><td></td><td>Create the class even if the controller already exists</td></tr> <tr><td>--invokable</td><td>(-i)</td><td>Generate a single method, invokable controller class</td></tr> <tr><td>--model</td><td>(-m)</td><td>Generate a resource controller for the given model</td></tr> <tr><td>--parent</td><td>(-p)</td><td>Generate a nested resource controller class</td></tr> <tr><td>--resource</td><td>(-r)</td><td>Generate a resource controller class</td></tr> <tr><td>--requests</td><td>(-R)</td><td>Generate FormRequest classes for store and update</td></tr> <tr><td>--singleton</td><td>(-s)</td><td>Generate a singleton resource controller class</td></tr> <tr><td>--creatable</td><td></td><td>Indicate that a singleton resource should be creatable</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Controller</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Controller</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Controller</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--api" shortcut="">
        <help><![CDATA[Exclude the create and edit methods from the controller]]></help>
      </option>
      <option name="--type" shortcut="" pattern="equals">
        <help><![CDATA[Manually specify the controller stub file to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the controller already exists]]></help>
      </option>
      <option name="--invokable" shortcut="-i">
        <help><![CDATA[Generate a single method, invokable controller class]]></help>
      </option>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[Generate a resource controller for the given model]]></help>
      </option>
      <option name="--parent" shortcut="-p" pattern="equals">
        <help><![CDATA[Generate a nested resource controller class]]></help>
      </option>
      <option name="--resource" shortcut="-r">
        <help><![CDATA[Generate a resource controller class]]></help>
      </option>
      <option name="--requests" shortcut="-R">
        <help><![CDATA[Generate FormRequest classes for store and update]]></help>
      </option>
      <option name="--singleton" shortcut="-s">
        <help><![CDATA[Generate a singleton resource controller class]]></help>
      </option>
      <option name="--creatable" shortcut="">
        <help><![CDATA[Indicate that a singleton resource should be creatable]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Controller]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Controller]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Controller]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:enum</name>
    <help><![CDATA[Create a new enum<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--string</td><td>(-s)</td><td>Generate a string backed enum.</td></tr> <tr><td>--int</td><td>(-i)</td><td>Generate an integer backed enum.</td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the enum even if the enum already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--string" shortcut="-s">
        <help><![CDATA[Generate a string backed enum.]]></help>
      </option>
      <option name="--int" shortcut="-i">
        <help><![CDATA[Generate an integer backed enum.]]></help>
      </option>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the enum even if the enum already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:event</name>
    <help><![CDATA[Create a new event class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the event already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the event already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:exception</name>
    <help><![CDATA[Create a new custom exception class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the exception already exists</td></tr> <tr><td>--render</td><td></td><td>Create the exception with an empty render method</td></tr> <tr><td>--report</td><td></td><td>Create the exception with an empty report method</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the exception already exists]]></help>
      </option>
      <option name="--render" shortcut="">
        <help><![CDATA[Create the exception with an empty render method]]></help>
      </option>
      <option name="--report" shortcut="">
        <help><![CDATA[Create the exception with an empty report method]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:factory</name>
    <help><![CDATA[Create a new model factory<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td>(-m)</td><td>The name of the model</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The name of the model]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:interface</name>
    <help><![CDATA[Create a new interface<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the interface even if the interface already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the interface even if the interface already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:job</name>
    <help><![CDATA[Create a new job class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the job already exists</td></tr> <tr><td>--sync</td><td></td><td>Indicates that the job should be synchronous</td></tr> <tr><td>--batched</td><td></td><td>Indicates that the job should be batchable</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Job</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Job</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Job</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the job already exists]]></help>
      </option>
      <option name="--sync" shortcut="">
        <help><![CDATA[Indicates that the job should be synchronous]]></help>
      </option>
      <option name="--batched" shortcut="">
        <help><![CDATA[Indicates that the job should be batchable]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Job]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Job]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Job]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:job-middleware</name>
    <help><![CDATA[Create a new job middleware class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the job middleware already exists</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Middleware</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Middleware</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Middleware</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the job middleware already exists]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Middleware]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Middleware]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Middleware]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:listener</name>
    <help><![CDATA[Create a new event listener class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--event</td><td>(-e)</td><td>The event class being listened for</td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the listener already exists</td></tr> <tr><td>--queued</td><td></td><td>Indicates the event listener should be queued</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Listener</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Listener</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Listener</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--event" shortcut="-e" pattern="equals">
        <help><![CDATA[The event class being listened for]]></help>
      </option>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the listener already exists]]></help>
      </option>
      <option name="--queued" shortcut="">
        <help><![CDATA[Indicates the event listener should be queued]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Listener]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Listener]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Listener]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:mail</name>
    <help><![CDATA[Create a new email class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the mailable already exists</td></tr> <tr><td>--markdown</td><td>(-m)</td><td>Create a new Markdown template for the mailable</td></tr> <tr><td>--view</td><td></td><td>Create a new Blade template for the mailable</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Mailable</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Mailable</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Mailable</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the mailable already exists]]></help>
      </option>
      <option name="--markdown" shortcut="-m" pattern="equals">
        <help><![CDATA[Create a new Markdown template for the mailable]]></help>
      </option>
      <option name="--view" shortcut="" pattern="equals">
        <help><![CDATA[Create a new Blade template for the mailable]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Mailable]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Mailable]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Mailable]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:middleware</name>
    <help><![CDATA[Create a new HTTP middleware class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Middleware</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Middleware</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Middleware</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Middleware]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Middleware]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Middleware]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:migration</name>
    <help><![CDATA[Create a new migration file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--create</td><td></td><td>The table to be created</td></tr> <tr><td>--table</td><td></td><td>The table to migrate</td></tr> <tr><td>--path</td><td></td><td>The location where the migration file should be created</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--fullpath</td><td></td><td>Output the full path of the migration (Deprecated)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--create" shortcut="" pattern="equals">
        <help><![CDATA[The table to be created]]></help>
      </option>
      <option name="--table" shortcut="" pattern="equals">
        <help><![CDATA[The table to migrate]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The location where the migration file should be created]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--fullpath" shortcut="">
        <help><![CDATA[Output the full path of the migration (Deprecated)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:model</name>
    <help><![CDATA[Create a new Eloquent model class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td>(-a)</td><td>Generate a migration, seeder, factory, policy, resource controller, and form request classes for the model</td></tr> <tr><td>--controller</td><td>(-c)</td><td>Create a new controller for the model</td></tr> <tr><td>--factory</td><td>(-f)</td><td>Create a new factory for the model</td></tr> <tr><td>--force</td><td></td><td>Create the class even if the model already exists</td></tr> <tr><td>--migration</td><td>(-m)</td><td>Create a new migration file for the model</td></tr> <tr><td>--morph-pivot</td><td></td><td>Indicates if the generated model should be a custom polymorphic intermediate table model</td></tr> <tr><td>--policy</td><td></td><td>Create a new policy for the model</td></tr> <tr><td>--seed</td><td>(-s)</td><td>Create a new seeder for the model</td></tr> <tr><td>--pivot</td><td>(-p)</td><td>Indicates if the generated model should be a custom intermediate table model</td></tr> <tr><td>--resource</td><td>(-r)</td><td>Indicates if the generated controller should be a resource controller</td></tr> <tr><td>--api</td><td></td><td>Indicates if the generated controller should be an API resource controller</td></tr> <tr><td>--requests</td><td>(-R)</td><td>Create new form request classes and use them in the resource controller</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Model</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Model</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Model</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--all" shortcut="-a">
        <help><![CDATA[Generate a migration, seeder, factory, policy, resource controller, and form request classes for the model]]></help>
      </option>
      <option name="--controller" shortcut="-c">
        <help><![CDATA[Create a new controller for the model]]></help>
      </option>
      <option name="--factory" shortcut="-f">
        <help><![CDATA[Create a new factory for the model]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the model already exists]]></help>
      </option>
      <option name="--migration" shortcut="-m">
        <help><![CDATA[Create a new migration file for the model]]></help>
      </option>
      <option name="--morph-pivot" shortcut="">
        <help><![CDATA[Indicates if the generated model should be a custom polymorphic intermediate table model]]></help>
      </option>
      <option name="--policy" shortcut="">
        <help><![CDATA[Create a new policy for the model]]></help>
      </option>
      <option name="--seed" shortcut="-s">
        <help><![CDATA[Create a new seeder for the model]]></help>
      </option>
      <option name="--pivot" shortcut="-p">
        <help><![CDATA[Indicates if the generated model should be a custom intermediate table model]]></help>
      </option>
      <option name="--resource" shortcut="-r">
        <help><![CDATA[Indicates if the generated controller should be a resource controller]]></help>
      </option>
      <option name="--api" shortcut="">
        <help><![CDATA[Indicates if the generated controller should be an API resource controller]]></help>
      </option>
      <option name="--requests" shortcut="-R">
        <help><![CDATA[Create new form request classes and use them in the resource controller]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Model]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Model]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Model]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:notification</name>
    <help><![CDATA[Create a new notification class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the notification already exists</td></tr> <tr><td>--markdown</td><td>(-m)</td><td>Create a new Markdown template for the notification</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the Notification</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the Notification</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the Notification</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the notification already exists]]></help>
      </option>
      <option name="--markdown" shortcut="-m" pattern="equals">
        <help><![CDATA[Create a new Markdown template for the notification]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the Notification]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the Notification]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the Notification]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:notifications-table</name>
    <help><![CDATA[Create a migration for the notifications table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:observer</name>
    <help><![CDATA[Create a new observer class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the observer already exists</td></tr> <tr><td>--model</td><td>(-m)</td><td>The model that the observer applies to</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the observer already exists]]></help>
      </option>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The model that the observer applies to]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:policy</name>
    <help><![CDATA[Create a new policy class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the policy already exists</td></tr> <tr><td>--model</td><td>(-m)</td><td>The model that the policy applies to</td></tr> <tr><td>--guard</td><td>(-g)</td><td>The guard that the policy relies on</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the policy already exists]]></help>
      </option>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The model that the policy applies to]]></help>
      </option>
      <option name="--guard" shortcut="-g" pattern="equals">
        <help><![CDATA[The guard that the policy relies on]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:provider</name>
    <help><![CDATA[Create a new service provider class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the provider already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the provider already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:queue-batches-table</name>
    <help><![CDATA[Create a migration for the batches database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:queue-failed-table</name>
    <help><![CDATA[Create a migration for the failed queue jobs database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:queue-table</name>
    <help><![CDATA[Create a migration for the queue jobs database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:request</name>
    <help><![CDATA[Create a new form request class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the request already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the request already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:resource</name>
    <help><![CDATA[Create a new resource<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the resource already exists</td></tr> <tr><td>--collection</td><td>(-c)</td><td>Create a resource collection</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the resource already exists]]></help>
      </option>
      <option name="--collection" shortcut="-c">
        <help><![CDATA[Create a resource collection]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:rule</name>
    <help><![CDATA[Create a new validation rule<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the rule already exists</td></tr> <tr><td>--implicit</td><td>(-i)</td><td>Generate an implicit rule</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the rule already exists]]></help>
      </option>
      <option name="--implicit" shortcut="-i">
        <help><![CDATA[Generate an implicit rule]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:scope</name>
    <help><![CDATA[Create a new scope class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the scope already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the scope already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:seeder</name>
    <help><![CDATA[Create a new seeder class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:session-table</name>
    <help><![CDATA[Create a migration for the session database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:test</name>
    <help><![CDATA[Create a new test class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the test even if the test already exists</td></tr> <tr><td>--unit</td><td>(-u)</td><td>Create a unit test</td></tr> <tr><td>--pest</td><td></td><td>Create a Pest test</td></tr> <tr><td>--phpunit</td><td></td><td>Create a PHPUnit test</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the test even if the test already exists]]></help>
      </option>
      <option name="--unit" shortcut="-u">
        <help><![CDATA[Create a unit test]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Create a Pest test]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Create a PHPUnit test]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:trait</name>
    <help><![CDATA[Create a new trait<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the trait even if the trait already exists</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the trait even if the trait already exists]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:view</name>
    <help><![CDATA[Create a new view<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--extension</td><td></td><td>The extension of the generated view</td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the view even if the view already exists</td></tr> <tr><td>--test</td><td></td><td>Generate an accompanying Test test for the View</td></tr> <tr><td>--pest</td><td></td><td>Generate an accompanying Pest test for the View</td></tr> <tr><td>--phpunit</td><td></td><td>Generate an accompanying PHPUnit test for the View</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--extension" shortcut="" pattern="equals">
        <help><![CDATA[The extension of the generated view]]></help>
      </option>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the view even if the view already exists]]></help>
      </option>
      <option name="--test" shortcut="">
        <help><![CDATA[Generate an accompanying Test test for the View]]></help>
      </option>
      <option name="--pest" shortcut="">
        <help><![CDATA[Generate an accompanying Pest test for the View]]></help>
      </option>
      <option name="--phpunit" shortcut="">
        <help><![CDATA[Generate an accompanying PHPUnit test for the View]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:fresh</name>
    <help><![CDATA[Drop all tables and re-run all migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--drop-views</td><td></td><td>Drop all tables and views</td></tr> <tr><td>--drop-types</td><td></td><td>Drop all tables and types (Postgres only)</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--schema-path</td><td></td><td>The path to a schema dump file</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--seeder</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--step</td><td></td><td>Force the migrations to be run so they can be rolled back individually</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--drop-views" shortcut="">
        <help><![CDATA[Drop all tables and views]]></help>
      </option>
      <option name="--drop-types" shortcut="">
        <help><![CDATA[Drop all tables and types (Postgres only)]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--schema-path" shortcut="" pattern="equals">
        <help><![CDATA[The path to a schema dump file]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--seeder" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--step" shortcut="">
        <help><![CDATA[Force the migrations to be run so they can be rolled back individually]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:install</name>
    <help><![CDATA[Create the migration repository<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:refresh</name>
    <help><![CDATA[Reset and re-run all migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--seeder</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--step</td><td></td><td>The number of migrations to be reverted & re-run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--seeder" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--step" shortcut="" pattern="equals">
        <help><![CDATA[The number of migrations to be reverted & re-run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:reset</name>
    <help><![CDATA[Rollback all database migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:rollback</name>
    <help><![CDATA[Rollback the last database migration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--step</td><td></td><td>The number of migrations to be reverted</td></tr> <tr><td>--batch</td><td></td><td>The batch of migrations (identified by their batch number) to be reverted</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--step" shortcut="" pattern="equals">
        <help><![CDATA[The number of migrations to be reverted]]></help>
      </option>
      <option name="--batch" shortcut="" pattern="equals">
        <help><![CDATA[The batch of migrations (identified by their batch number) to be reverted]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:status</name>
    <help><![CDATA[Show the status of each migration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--pending</td><td></td><td>Only list pending migrations</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to use</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--pending" shortcut="" pattern="equals">
        <help><![CDATA[Only list pending migrations]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to use]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>model:prune</name>
    <help><![CDATA[Prune models that are no longer needed<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td></td><td>Class names of the models to be pruned</td></tr> <tr><td>--except</td><td></td><td>Class names of the models to be excluded from pruning</td></tr> <tr><td>--path</td><td></td><td>Absolute path(s) to directories where models are located</td></tr> <tr><td>--chunk</td><td></td><td>The number of models to retrieve per chunk of models to be deleted</td></tr> <tr><td>--pretend</td><td></td><td>Display the number of prunable records found instead of deleting them</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--model" shortcut="" pattern="equals">
        <help><![CDATA[Class names of the models to be pruned]]></help>
      </option>
      <option name="--except" shortcut="" pattern="equals">
        <help><![CDATA[Class names of the models to be excluded from pruning]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[Absolute path(s) to directories where models are located]]></help>
      </option>
      <option name="--chunk" shortcut="" pattern="equals">
        <help><![CDATA[The number of models to retrieve per chunk of models to be deleted]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Display the number of prunable records found instead of deleting them]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>model:show</name>
    <help><![CDATA[Show information about an Eloquent model<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--json</td><td></td><td>Output the model as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>model</params>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the model as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>optimize:clear</name>
    <help><![CDATA[Remove the cached bootstrap files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--except</td><td>(-e)</td><td>The commands to skip</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--except" shortcut="-e" pattern="equals">
        <help><![CDATA[The commands to skip]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>package:discover</name>
    <help><![CDATA[Rebuild the cached package manifest<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:clear</name>
    <help><![CDATA[Delete all of the jobs from the specified queue<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--queue</td><td></td><td>The name of the queue to clear</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The name of the queue to clear]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:failed</name>
    <help><![CDATA[List all of the failed queue jobs<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:flush</name>
    <help><![CDATA[Flush all of the failed queue jobs<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain failed job data</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain failed job data]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:forget</name>
    <help><![CDATA[Delete a failed queue job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:listen</name>
    <help><![CDATA[Listen to a given queue<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>The name of the worker</td></tr> <tr><td>--delay</td><td></td><td>The number of seconds to delay failed jobs (Deprecated)</td></tr> <tr><td>--backoff</td><td></td><td>The number of seconds to wait before retrying a job that encountered an uncaught exception</td></tr> <tr><td>--force</td><td></td><td>Force the worker to run even in maintenance mode</td></tr> <tr><td>--memory</td><td></td><td>The memory limit in megabytes</td></tr> <tr><td>--queue</td><td></td><td>The queue to listen on</td></tr> <tr><td>--sleep</td><td></td><td>Number of seconds to sleep when no job is available</td></tr> <tr><td>--rest</td><td></td><td>Number of seconds to rest between jobs</td></tr> <tr><td>--timeout</td><td></td><td>The number of seconds a child process can run</td></tr> <tr><td>--tries</td><td></td><td>Number of times to attempt a job before logging it failed</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the worker]]></help>
      </option>
      <option name="--delay" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to delay failed jobs (Deprecated)]]></help>
      </option>
      <option name="--backoff" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to wait before retrying a job that encountered an uncaught exception]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the worker to run even in maintenance mode]]></help>
      </option>
      <option name="--memory" shortcut="" pattern="equals">
        <help><![CDATA[The memory limit in megabytes]]></help>
      </option>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The queue to listen on]]></help>
      </option>
      <option name="--sleep" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to sleep when no job is available]]></help>
      </option>
      <option name="--rest" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to rest between jobs]]></help>
      </option>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds a child process can run]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[Number of times to attempt a job before logging it failed]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:monitor</name>
    <help><![CDATA[Monitor the size of the specified queues<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--max</td><td></td><td>The maximum number of jobs that can be on the queue before an event is dispatched</td></tr> <tr><td>--json</td><td></td><td>Output the queue size as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>queues</params>
    <optionsBefore>
      <option name="--max" shortcut="" pattern="equals">
        <help><![CDATA[The maximum number of jobs that can be on the queue before an event is dispatched]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the queue size as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:prune-batches</name>
    <help><![CDATA[Prune stale entries from the batches database<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain batch data</td></tr> <tr><td>--unfinished</td><td></td><td>The number of hours to retain unfinished batch data</td></tr> <tr><td>--cancelled</td><td></td><td>The number of hours to retain cancelled batch data</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain batch data]]></help>
      </option>
      <option name="--unfinished" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain unfinished batch data]]></help>
      </option>
      <option name="--cancelled" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain cancelled batch data]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:prune-failed</name>
    <help><![CDATA[Prune stale entries from the failed jobs table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain failed jobs data</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain failed jobs data]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:restart</name>
    <help><![CDATA[Restart queue worker daemons after their current job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:retry</name>
    <help><![CDATA[Retry a failed queue job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--queue</td><td></td><td>Retry all of the failed jobs for the specified queue</td></tr> <tr><td>--range</td><td></td><td>Range of job IDs (numeric) to be retried (e.g. 1-5)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id[=null]</params>
    <optionsBefore>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[Retry all of the failed jobs for the specified queue]]></help>
      </option>
      <option name="--range" shortcut="" pattern="equals">
        <help><![CDATA[Range of job IDs (numeric) to be retried (e.g. 1-5)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:retry-batch</name>
    <help><![CDATA[Retry the failed jobs for a batch<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--isolated</td><td></td><td>Do not run the command if another instance of the command is already running</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id[=null]</params>
    <optionsBefore>
      <option name="--isolated" shortcut="" pattern="equals">
        <help><![CDATA[Do not run the command if another instance of the command is already running]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:work</name>
    <help><![CDATA[Start processing jobs on the queue as a daemon<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>The name of the worker</td></tr> <tr><td>--queue</td><td></td><td>The names of the queues to work</td></tr> <tr><td>--daemon</td><td></td><td>Run the worker in daemon mode (Deprecated)</td></tr> <tr><td>--once</td><td></td><td>Only process the next job on the queue</td></tr> <tr><td>--stop-when-empty</td><td></td><td>Stop when the queue is empty</td></tr> <tr><td>--delay</td><td></td><td>The number of seconds to delay failed jobs (Deprecated)</td></tr> <tr><td>--backoff</td><td></td><td>The number of seconds to wait before retrying a job that encountered an uncaught exception</td></tr> <tr><td>--max-jobs</td><td></td><td>The number of jobs to process before stopping</td></tr> <tr><td>--max-time</td><td></td><td>The maximum number of seconds the worker should run</td></tr> <tr><td>--force</td><td></td><td>Force the worker to run even in maintenance mode</td></tr> <tr><td>--memory</td><td></td><td>The memory limit in megabytes</td></tr> <tr><td>--sleep</td><td></td><td>Number of seconds to sleep when no job is available</td></tr> <tr><td>--rest</td><td></td><td>Number of seconds to rest between jobs</td></tr> <tr><td>--timeout</td><td></td><td>The number of seconds a child process can run</td></tr> <tr><td>--tries</td><td></td><td>Number of times to attempt a job before logging it failed</td></tr> <tr><td>--json</td><td></td><td>Output the queue worker information as JSON</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the worker]]></help>
      </option>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The names of the queues to work]]></help>
      </option>
      <option name="--daemon" shortcut="">
        <help><![CDATA[Run the worker in daemon mode (Deprecated)]]></help>
      </option>
      <option name="--once" shortcut="">
        <help><![CDATA[Only process the next job on the queue]]></help>
      </option>
      <option name="--stop-when-empty" shortcut="">
        <help><![CDATA[Stop when the queue is empty]]></help>
      </option>
      <option name="--delay" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to delay failed jobs (Deprecated)]]></help>
      </option>
      <option name="--backoff" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to wait before retrying a job that encountered an uncaught exception]]></help>
      </option>
      <option name="--max-jobs" shortcut="" pattern="equals">
        <help><![CDATA[The number of jobs to process before stopping]]></help>
      </option>
      <option name="--max-time" shortcut="" pattern="equals">
        <help><![CDATA[The maximum number of seconds the worker should run]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the worker to run even in maintenance mode]]></help>
      </option>
      <option name="--memory" shortcut="" pattern="equals">
        <help><![CDATA[The memory limit in megabytes]]></help>
      </option>
      <option name="--sleep" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to sleep when no job is available]]></help>
      </option>
      <option name="--rest" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to rest between jobs]]></help>
      </option>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds a child process can run]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[Number of times to attempt a job before logging it failed]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the queue worker information as JSON]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:cache</name>
    <help><![CDATA[Create a route cache file for faster route registration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:clear</name>
    <help><![CDATA[Remove the route cache file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:list</name>
    <help><![CDATA[List all registered routes<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--json</td><td></td><td>Output the route list as JSON</td></tr> <tr><td>--method</td><td></td><td>Filter the routes by method</td></tr> <tr><td>--action</td><td></td><td>Filter the routes by action</td></tr> <tr><td>--name</td><td></td><td>Filter the routes by name</td></tr> <tr><td>--domain</td><td></td><td>Filter the routes by domain</td></tr> <tr><td>--path</td><td></td><td>Only show routes matching the given path pattern</td></tr> <tr><td>--except-path</td><td></td><td>Do not display the routes matching the given path pattern</td></tr> <tr><td>--reverse</td><td>(-r)</td><td>Reverse the ordering of the routes</td></tr> <tr><td>--sort</td><td></td><td>The column (domain, method, uri, name, action, middleware, definition) to sort by</td></tr> <tr><td>--except-vendor</td><td></td><td>Do not display routes defined by vendor packages</td></tr> <tr><td>--only-vendor</td><td></td><td>Only display routes defined by vendor packages</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the route list as JSON]]></help>
      </option>
      <option name="--method" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by method]]></help>
      </option>
      <option name="--action" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by action]]></help>
      </option>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by name]]></help>
      </option>
      <option name="--domain" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by domain]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[Only show routes matching the given path pattern]]></help>
      </option>
      <option name="--except-path" shortcut="" pattern="equals">
        <help><![CDATA[Do not display the routes matching the given path pattern]]></help>
      </option>
      <option name="--reverse" shortcut="-r">
        <help><![CDATA[Reverse the ordering of the routes]]></help>
      </option>
      <option name="--sort" shortcut="" pattern="equals">
        <help><![CDATA[The column (domain, method, uri, name, action, middleware, definition) to sort by]]></help>
      </option>
      <option name="--except-vendor" shortcut="">
        <help><![CDATA[Do not display routes defined by vendor packages]]></help>
      </option>
      <option name="--only-vendor" shortcut="">
        <help><![CDATA[Only display routes defined by vendor packages]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sail:add</name>
    <help><![CDATA[Add a service to an existing Sail installation<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>services[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sail:install</name>
    <help><![CDATA[Install Laravel Sail's default Docker Compose file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--with</td><td></td><td>The services that should be included in the installation</td></tr> <tr><td>--devcontainer</td><td></td><td>Create a .devcontainer configuration directory</td></tr> <tr><td>--php</td><td></td><td>The PHP version that should be used</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--with" shortcut="" pattern="equals">
        <help><![CDATA[The services that should be included in the installation]]></help>
      </option>
      <option name="--devcontainer" shortcut="">
        <help><![CDATA[Create a .devcontainer configuration directory]]></help>
      </option>
      <option name="--php" shortcut="" pattern="equals">
        <help><![CDATA[The PHP version that should be used]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sail:publish</name>
    <help><![CDATA[Publish the Laravel Sail Docker files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sanctum:prune-expired</name>
    <help><![CDATA[Prune tokens expired for more than specified number of hours<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain expired Sanctum tokens</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain expired Sanctum tokens]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:clear-cache</name>
    <help><![CDATA[Delete the cached mutex files created by scheduler<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:finish</name>
    <help><![CDATA[Handle the completion of a scheduled command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id code[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:interrupt</name>
    <help><![CDATA[Interrupt the current schedule run<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:list</name>
    <help><![CDATA[List all scheduled tasks<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--timezone</td><td></td><td>The timezone that times should be displayed in</td></tr> <tr><td>--next</td><td></td><td>Sort the listed tasks by their next due date</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--timezone" shortcut="" pattern="equals">
        <help><![CDATA[The timezone that times should be displayed in]]></help>
      </option>
      <option name="--next" shortcut="">
        <help><![CDATA[Sort the listed tasks by their next due date]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:run</name>
    <help><![CDATA[Run the scheduled commands<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--whisper</td><td></td><td>Do not output message indicating that no jobs were ready to run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--whisper" shortcut="">
        <help><![CDATA[Do not output message indicating that no jobs were ready to run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:test</name>
    <help><![CDATA[Run a scheduled command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>The name of the scheduled command to run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the scheduled command to run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:work</name>
    <help><![CDATA[Start the schedule worker<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--run-output-file</td><td></td><td>The file to direct <b>schedule:run</b> output to</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--run-output-file" shortcut="" pattern="equals">
        <help><![CDATA[The file to direct <b>schedule:run</b> output to]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schema:dump</name>
    <help><![CDATA[Dump the given database schema<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--path</td><td></td><td>The path where the schema dump file should be stored</td></tr> <tr><td>--prune</td><td></td><td>Delete all existing migration files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path where the schema dump file should be stored]]></help>
      </option>
      <option name="--prune" shortcut="">
        <help><![CDATA[Delete all existing migration files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>storage:link</name>
    <help><![CDATA[Create the symbolic links configured for the application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--relative</td><td></td><td>Create the symbolic link using relative paths</td></tr> <tr><td>--force</td><td></td><td>Recreate existing symbolic links</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--relative" shortcut="">
        <help><![CDATA[Create the symbolic link using relative paths]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Recreate existing symbolic links]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>storage:unlink</name>
    <help><![CDATA[Delete existing symbolic links configured for the application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>stub:publish</name>
    <help><![CDATA[Publish all stubs that are available for customization<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--existing</td><td></td><td>Publish and overwrite only the files that have already been published</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--existing" shortcut="">
        <help><![CDATA[Publish and overwrite only the files that have already been published]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>vendor:publish</name>
    <help><![CDATA[Publish any publishable assets from vendor packages<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--existing</td><td></td><td>Publish and overwrite only the files that have already been published</td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing files</td></tr> <tr><td>--all</td><td></td><td>Publish assets for all service providers without prompt</td></tr> <tr><td>--provider</td><td></td><td>The service provider that has assets you want to publish</td></tr> <tr><td>--tag</td><td></td><td>One or many tags that have assets you want to publish</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--existing" shortcut="">
        <help><![CDATA[Publish and overwrite only the files that have already been published]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing files]]></help>
      </option>
      <option name="--all" shortcut="">
        <help><![CDATA[Publish assets for all service providers without prompt]]></help>
      </option>
      <option name="--provider" shortcut="" pattern="equals">
        <help><![CDATA[The service provider that has assets you want to publish]]></help>
      </option>
      <option name="--tag" shortcut="" pattern="equals">
        <help><![CDATA[One or many tags that have assets you want to publish]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>view:cache</name>
    <help><![CDATA[Compile all of the application's Blade templates<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>view:clear</name>
    <help><![CDATA[Clear all compiled view files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>ziggy:generate</name>
    <help><![CDATA[Generate a JavaScript file containing Ziggy’s routes and configuration.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--types</td><td></td><td>Generate a TypeScript declaration file.</td></tr> <tr><td>--types-only</td><td></td><td>Generate only a TypeScript declaration file.</td></tr> <tr><td>--url</td><td></td><td></td></tr> <tr><td>--group</td><td></td><td></td></tr> <tr><td>--except</td><td></td><td>Route name patterns to exclude.</td></tr> <tr><td>--only</td><td></td><td>Route name patterns to include.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--silent</td><td></td><td>Do not output any message</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Only errors are displayed. All other output is suppressed</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>path[=null]</params>
    <optionsBefore>
      <option name="--types" shortcut="">
        <help><![CDATA[Generate a TypeScript declaration file.]]></help>
      </option>
      <option name="--types-only" shortcut="">
        <help><![CDATA[Generate only a TypeScript declaration file.]]></help>
      </option>
      <option name="--url" shortcut="" pattern="equals" />
      <option name="--group" shortcut="" pattern="equals" />
      <option name="--except" shortcut="" pattern="equals">
        <help><![CDATA[Route name patterns to exclude.]]></help>
      </option>
      <option name="--only" shortcut="" pattern="equals">
        <help><![CDATA[Route name patterns to include.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--silent" shortcut="">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Only errors are displayed. All other output is suppressed]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
</framework>

