{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.21", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@inertiajs/react": "^2.0.14", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-react": "^4.6.0", "framer-motion": "^12.23.6", "leaflet": "^1.9.4", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^4.2.1", "swiper": "^11.2.10"}}