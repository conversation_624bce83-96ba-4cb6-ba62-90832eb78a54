import{r as y,R as Le,j as m,t as _,x as Si,J as Ci,Q as Mi}from"./app2.js";import{b as Ne,c as ji,F as Di,a as Ri}from"./XMarkIcon-DvlfLOeu.js";import{F as Li}from"./MapPinIcon-HgHdBN5q.js";import{F as Ni}from"./ClockIcon-D-sImXRf.js";import{F as Ei}from"./PaperAirplaneIcon-5vIWJYTj.js";import{F as Fi}from"./CheckCircleIcon-C2jnGy7x.js";import{F as Bi}from"./XCircleIcon-Bp7te63t.js";const Ee=y.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Ht=y.createContext({});function ki(){return y.useContext(Ht).visualElement}const Kt=y.createContext(null),et=typeof document<"u",ne=et?y.useLayoutEffect:y.useEffect,cs=y.createContext({strict:!1});function Oi(t,e,n,s){const i=ki(),r=y.useContext(cs),o=y.useContext(Kt),a=y.useContext(Ee).reducedMotion,l=y.useRef();s=s||r.renderer,!l.current&&s&&(l.current=s(t,{visualState:e,parent:i,props:n,presenceId:o?o.id:void 0,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;return ne(()=>{c&&c.render()}),ne(()=>{c&&c.animationState&&c.animationState.animateChanges()}),ne(()=>()=>c&&c.notify("Unmount"),[]),c}function ot(t){return typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Ii(t,e,n){return y.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ot(n)&&(n.current=s))},[e])}function wt(t){return typeof t=="string"||Array.isArray(t)}function Yt(t){return typeof t=="object"&&typeof t.start=="function"}const _i=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function qt(t){return Yt(t.animate)||_i.some(e=>wt(t[e]))}function us(t){return!!(qt(t)||t.variants)}function Ui(t,e){if(qt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||wt(n)?n:void 0,animate:wt(s)?s:void 0}}return t.inherit!==!1?e:{}}function $i(t){const{initial:e,animate:n}=Ui(t,y.useContext(Ht));return y.useMemo(()=>({initial:e,animate:n}),[ln(e),ln(n)])}function ln(t){return Array.isArray(t)?t.join(" "):t}const G=t=>({isEnabled:e=>t.some(n=>!!e[n])}),Vt={measureLayout:G(["layout","layoutId","drag"]),animation:G(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:G(["exit"]),drag:G(["drag","dragControls"]),focus:G(["whileFocus"]),hover:G(["whileHover","onHoverStart","onHoverEnd"]),tap:G(["whileTap","onTap","onTapStart","onTapCancel"]),pan:G(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:G(["whileInView","onViewportEnter","onViewportLeave"])};function zi(t){for(const e in t)e==="projectionNodeConstructor"?Vt.projectionNodeConstructor=t[e]:Vt[e].Component=t[e]}function Fe(t){const e=y.useRef(null);return e.current===null&&(e.current=t()),e.current}const pt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let Gi=1;function Wi(){return Fe(()=>{if(pt.hasEverUpdated)return Gi++})}const fs=y.createContext({});class Xi extends Le.Component{getSnapshotBeforeUpdate(){const{visualElement:e,props:n}=this.props;return e&&e.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const ds=y.createContext({}),Hi=Symbol.for("motionComponentSymbol");function Ki({preloadedFeatures:t,createVisualElement:e,projectionNodeConstructor:n,useRender:s,useVisualState:i,Component:r}){t&&zi(t);function o(l,c){const u={...y.useContext(Ee),...l,layoutId:Yi(l)},{isStatic:f}=u;let d=null;const h=$i(l),p=f?void 0:Wi(),g=i(l,f);if(!f&&et){h.visualElement=Oi(r,g,u,e);const w=y.useContext(cs).strict,v=y.useContext(ds);h.visualElement&&(d=h.visualElement.loadFeatures(u,w,t,p,n||Vt.projectionNodeConstructor,v))}return y.createElement(Xi,{visualElement:h.visualElement,props:u},d,y.createElement(Ht.Provider,{value:h},s(r,l,p,Ii(g,h.visualElement,c),g,f,h.visualElement)))}const a=y.forwardRef(o);return a[Hi]=r,a}function Yi({layoutId:t}){const e=y.useContext(fs).id;return e&&t!==void 0?e+"-"+t:t}function qi(t){function e(s,i={}){return Ki(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const Zi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Be(t){return typeof t!="string"||t.includes("-")?!1:!!(Zi.indexOf(t)>-1||/[A-Z]/.test(t))}const Ft={};function Qi(t){Object.assign(Ft,t)}const Bt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],nt=new Set(Bt);function hs(t,{layout:e,layoutId:n}){return nt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Ft[t]||t==="opacity")}const $=t=>!!(t!=null&&t.getVelocity),Ji={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},to=(t,e)=>Bt.indexOf(t)-Bt.indexOf(e);function eo({transform:t,transformKeys:e},{enableHardwareAcceleration:n=!0,allowTransformNone:s=!0},i,r){let o="";e.sort(to);for(const a of e)o+=`${Ji[a]||a}(${t[a]}) `;return n&&!t.z&&(o+="translateZ(0)"),o=o.trim(),r?o=r(t,i?"":o):s&&i&&(o="none"),o}function ms(t){return t.startsWith("--")}const no=(t,e)=>e&&typeof t=="number"?e.transform(t):t,ct=(t,e,n)=>Math.min(Math.max(n,t),e),st={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},gt={...st,transform:t=>ct(0,1,t)},jt={...st,default:1},yt=t=>Math.round(t*1e5)/1e5,Tt=/(-)?([\d]*\.?[\d])+/g,pe=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,so=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function At(t){return typeof t=="string"}const St=t=>({test:e=>At(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),X=St("deg"),U=St("%"),T=St("px"),io=St("vh"),oo=St("vw"),cn={...U,parse:t=>U.parse(t)/100,transform:t=>U.transform(t*100)},un={...st,transform:Math.round},ps={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,size:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:jt,scaleX:jt,scaleY:jt,scaleZ:jt,skew:X,skewX:X,skewY:X,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:gt,originX:cn,originY:cn,originZ:T,zIndex:un,fillOpacity:gt,strokeOpacity:gt,numOctaves:un};function ke(t,e,n,s){const{style:i,vars:r,transform:o,transformKeys:a,transformOrigin:l}=t;a.length=0;let c=!1,u=!1,f=!0;for(const d in e){const h=e[d];if(ms(d)){r[d]=h;continue}const p=ps[d],g=no(h,p);if(nt.has(d)){if(c=!0,o[d]=g,a.push(d),!f)continue;h!==(p.default||0)&&(f=!1)}else d.startsWith("origin")?(u=!0,l[d]=g):i[d]=g}if(e.transform||(c||s?i.transform=eo(t,n,f,s):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:h="50%",originZ:p=0}=l;i.transformOrigin=`${d} ${h} ${p}`}}const Oe=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function gs(t,e,n){for(const s in e)!$(e[s])&&!hs(s,n)&&(t[s]=e[s])}function ro({transformTemplate:t},e,n){return y.useMemo(()=>{const s=Oe();return ke(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function ao(t,e,n){const s=t.style||{},i={};return gs(i,s,t),Object.assign(i,ro(t,e,n)),t.transformValues?t.transformValues(i):i}function lo(t,e,n){const s={},i=ao(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),s.style=i,s}const co=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],uo=["whileTap","onTap","onTapStart","onTapCancel"],fo=["onPan","onPanStart","onPanSessionStart","onPanEnd"],ho=["whileInView","onViewportEnter","onViewportLeave","viewport"],mo=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...ho,...uo,...co,...fo]);function kt(t){return mo.has(t)}let ys=t=>!kt(t);function po(t){t&&(ys=e=>e.startsWith("on")?!kt(e):t(e))}try{po(require("@emotion/is-prop-valid").default)}catch{}function go(t,e,n){const s={};for(const i in t)(ys(i)||n===!0&&kt(i)||!e&&!kt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function fn(t,e,n){return typeof t=="string"?t:T.transform(e+n*t)}function yo(t,e,n){const s=fn(e,t.x,t.width),i=fn(n,t.y,t.height);return`${s} ${i}`}const vo={offset:"stroke-dashoffset",array:"stroke-dasharray"},xo={offset:"strokeDashoffset",array:"strokeDasharray"};function bo(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?vo:xo;t[r.offset]=T.transform(-s);const o=T.transform(e),a=T.transform(n);t[r.array]=`${o} ${a}`}function Ie(t,{attrX:e,attrY:n,originX:s,originY:i,pathLength:r,pathSpacing:o=1,pathOffset:a=0,...l},c,u,f){if(ke(t,l,c,f),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:h,dimensions:p}=t;d.transform&&(p&&(h.transform=d.transform),delete d.transform),p&&(s!==void 0||i!==void 0||h.transform)&&(h.transformOrigin=yo(p,s!==void 0?s:.5,i!==void 0?i:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),r!==void 0&&bo(d,r,o,a,!1)}const vs=()=>({...Oe(),attrs:{}}),_e=t=>typeof t=="string"&&t.toLowerCase()==="svg";function wo(t,e,n,s){const i=y.useMemo(()=>{const r=vs();return Ie(r,e,{enableHardwareAcceleration:!1},_e(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};gs(r,t.style,t),i.style={...r,...i.style}}return i}function Vo(t=!1){return(n,s,i,r,{latestValues:o},a)=>{const c=(Be(n)?wo:lo)(s,o,a,n),f={...go(s,typeof n=="string",t),...c,ref:r};return i&&(f["data-projection-id"]=i),y.createElement(n,f)}}const Ue=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function xs(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const bs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ws(t,e,n,s){xs(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(bs.has(i)?i:Ue(i),e.attrs[i])}function $e(t){const{style:e}=t,n={};for(const s in e)($(e[s])||hs(s,t))&&(n[s]=e[s]);return n}function Vs(t){const e=$e(t);for(const n in t)if($(t[n])){const s=n==="x"||n==="y"?"attr"+n.toUpperCase():n;e[s]=t[n]}return e}function ze(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}const Ot=t=>Array.isArray(t),To=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),Po=t=>Ot(t)?t[t.length-1]||0:t;function Nt(t){const e=$(t)?t.get():t;return To(e)?e.toValue():e}function Ao({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,r){const o={latestValues:So(s,i,r,t),renderState:e()};return n&&(o.mount=a=>n(s,a,o)),o}const Ts=t=>(e,n)=>{const s=y.useContext(Ht),i=y.useContext(Kt),r=()=>Ao(t,e,s,i);return n?r():Fe(r)};function So(t,e,n,s){const i={},r=s(t);for(const d in r)i[d]=Nt(r[d]);let{initial:o,animate:a}=t;const l=qt(t),c=us(t);e&&c&&!l&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const f=u?a:o;return f&&typeof f!="boolean"&&!Yt(f)&&(Array.isArray(f)?f:[f]).forEach(h=>{const p=ze(t,h);if(!p)return;const{transitionEnd:g,transition:w,...v}=p;for(const V in v){let x=v[V];if(Array.isArray(x)){const b=u?x.length-1:0;x=x[b]}x!==null&&(i[V]=x)}for(const V in g)i[V]=g[V]}),i}const Co={useVisualState:Ts({scrapeMotionValuesFromProps:Vs,createRenderState:vs,onMount:(t,e,{renderState:n,latestValues:s})=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}Ie(n,s,{enableHardwareAcceleration:!1},_e(e.tagName),t.transformTemplate),ws(e,n)}})},Mo={useVisualState:Ts({scrapeMotionValuesFromProps:$e,createRenderState:Oe})};function jo(t,{forwardMotionProps:e=!1},n,s,i){return{...Be(t)?Co:Mo,preloadedFeatures:n,useRender:Vo(e),createVisualElement:s,projectionNodeConstructor:i,Component:t}}var A;(function(t){t.Animate="animate",t.Hover="whileHover",t.Tap="whileTap",t.Drag="whileDrag",t.Focus="whileFocus",t.InView="whileInView",t.Exit="exit"})(A||(A={}));function Zt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function ge(t,e,n,s){y.useEffect(()=>{const i=t.current;if(n&&i)return Zt(i,e,n,s)},[t,e,n,s])}function Do({whileFocus:t,visualElement:e}){const{animationState:n}=e,s=()=>{n&&n.setActive(A.Focus,!0)},i=()=>{n&&n.setActive(A.Focus,!1)};ge(e,"focus",t?s:void 0),ge(e,"blur",t?i:void 0)}function Ps(t){return typeof PointerEvent<"u"&&t instanceof PointerEvent?t.pointerType==="mouse":t instanceof MouseEvent}function As(t){return!!t.touches}function Ro(t){return e=>{const n=e instanceof MouseEvent;(!n||n&&e.button===0)&&t(e)}}const Lo={pageX:0,pageY:0};function No(t,e="page"){const s=t.touches[0]||t.changedTouches[0]||Lo;return{x:s[e+"X"],y:s[e+"Y"]}}function Eo(t,e="page"){return{x:t[e+"X"],y:t[e+"Y"]}}function Ge(t,e="page"){return{point:As(t)?No(t,e):Eo(t,e)}}const Ss=(t,e=!1)=>{const n=s=>t(s,Ge(s));return e?Ro(n):n},Fo=()=>et&&window.onpointerdown===null,Bo=()=>et&&window.ontouchstart===null,ko=()=>et&&window.onmousedown===null,Oo={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},Io={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function Cs(t){return Fo()?t:Bo()?Io[t]:ko()?Oo[t]:t}function lt(t,e,n,s){return Zt(t,Cs(e),Ss(n,e==="pointerdown"),s)}function It(t,e,n,s){return ge(t,Cs(e),n&&Ss(n,e==="pointerdown"),s)}function Ms(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const dn=Ms("dragHorizontal"),hn=Ms("dragVertical");function js(t){let e=!1;if(t==="y")e=hn();else if(t==="x")e=dn();else{const n=dn(),s=hn();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Ds(){const t=js(!0);return t?(t(),!1):!0}function mn(t,e,n){return(s,i)=>{!Ps(s)||Ds()||(t.animationState&&t.animationState.setActive(A.Hover,e),n&&n(s,i))}}function _o({onHoverStart:t,onHoverEnd:e,whileHover:n,visualElement:s}){It(s,"pointerenter",t||n?mn(s,!0,t):void 0,{passive:!t}),It(s,"pointerleave",e||n?mn(s,!1,e):void 0,{passive:!e})}const Rs=(t,e)=>e?t===e?!0:Rs(t,e.parentElement):!1;function Ls(t){return y.useEffect(()=>()=>t(),[])}const Uo=(t,e)=>n=>e(t(n)),Qt=(...t)=>t.reduce(Uo);function $o({onTap:t,onTapStart:e,onTapCancel:n,whileTap:s,visualElement:i}){const r=t||e||n||s,o=y.useRef(!1),a=y.useRef(null),l={passive:!(e||t||n||h)};function c(){a.current&&a.current(),a.current=null}function u(){return c(),o.current=!1,i.animationState&&i.animationState.setActive(A.Tap,!1),!Ds()}function f(p,g){u()&&(Rs(i.current,p.target)?t&&t(p,g):n&&n(p,g))}function d(p,g){u()&&n&&n(p,g)}function h(p,g){c(),!o.current&&(o.current=!0,a.current=Qt(lt(window,"pointerup",f,l),lt(window,"pointercancel",d,l)),i.animationState&&i.animationState.setActive(A.Tap,!0),e&&e(p,g))}It(i,"pointerdown",r?h:void 0,l),Ls(c)}const ye=new WeakMap,se=new WeakMap,zo=t=>{const e=ye.get(t.target);e&&e(t)},Go=t=>{t.forEach(zo)};function Wo({root:t,...e}){const n=t||document;se.has(n)||se.set(n,{});const s=se.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Go,{root:t,...e})),s[i]}function Xo(t,e,n){const s=Wo(e);return ye.set(t,n),s.observe(t),()=>{ye.delete(t),s.unobserve(t)}}function Ho({visualElement:t,whileInView:e,onViewportEnter:n,onViewportLeave:s,viewport:i={}}){const r=y.useRef({hasEnteredView:!1,isInView:!1});let o=!!(e||n||s);i.once&&r.current.hasEnteredView&&(o=!1),(typeof IntersectionObserver>"u"?qo:Yo)(o,r.current,t,i)}const Ko={some:0,all:1};function Yo(t,e,n,{root:s,margin:i,amount:r="some",once:o}){y.useEffect(()=>{if(!t||!n.current)return;const a={root:s==null?void 0:s.current,rootMargin:i,threshold:typeof r=="number"?r:Ko[r]},l=c=>{const{isIntersecting:u}=c;if(e.isInView===u||(e.isInView=u,o&&!u&&e.hasEnteredView))return;u&&(e.hasEnteredView=!0),n.animationState&&n.animationState.setActive(A.InView,u);const f=n.getProps(),d=u?f.onViewportEnter:f.onViewportLeave;d&&d(c)};return Xo(n.current,a,l)},[t,s,i,r])}function qo(t,e,n,{fallback:s=!0}){y.useEffect(()=>{!t||!s||requestAnimationFrame(()=>{e.hasEnteredView=!0;const{onViewportEnter:i}=n.getProps();i&&i(null),n.animationState&&n.animationState.setActive(A.InView,!0)})},[t])}const K=t=>e=>(t(e),null),Zo={inView:K(Ho),tap:K($o),focus:K(Do),hover:K(_o)};function Ns(){const t=y.useContext(Kt);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=y.useId();return y.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}function Es(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Qo=t=>/^\-?\d*\.?\d+$/.test(t),Jo=t=>/^0[^.\s]+$/.test(t),W={delta:0,timestamp:0},Fs=1/60*1e3,tr=typeof performance<"u"?()=>performance.now():()=>Date.now(),Bs=typeof window<"u"?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(tr()),Fs);function er(t){let e=[],n=[],s=0,i=!1,r=!1;const o=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const f=u&&i,d=f?e:n;return c&&o.add(l),d.indexOf(l)===-1&&(d.push(l),f&&i&&(s=e.length)),l},cancel:l=>{const c=n.indexOf(l);c!==-1&&n.splice(c,1),o.delete(l)},process:l=>{if(i){r=!0;return}if(i=!0,[e,n]=[n,e],n.length=0,s=e.length,s)for(let c=0;c<s;c++){const u=e[c];u(l),o.has(u)&&(a.schedule(u),t())}i=!1,r&&(r=!1,a.process(l))}};return a}const nr=40;let ve=!0,Pt=!1,xe=!1;const Ct=["read","update","preRender","render","postRender"],Jt=Ct.reduce((t,e)=>(t[e]=er(()=>Pt=!0),t),{}),N=Ct.reduce((t,e)=>{const n=Jt[e];return t[e]=(s,i=!1,r=!1)=>(Pt||ir(),n.schedule(s,i,r)),t},{}),Y=Ct.reduce((t,e)=>(t[e]=Jt[e].cancel,t),{}),ie=Ct.reduce((t,e)=>(t[e]=()=>Jt[e].process(W),t),{}),sr=t=>Jt[t].process(W),ks=t=>{Pt=!1,W.delta=ve?Fs:Math.max(Math.min(t-W.timestamp,nr),1),W.timestamp=t,xe=!0,Ct.forEach(sr),xe=!1,Pt&&(ve=!1,Bs(ks))},ir=()=>{Pt=!0,ve=!0,xe||Bs(ks)};function We(t,e){t.indexOf(e)===-1&&t.push(e)}function Xe(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class He{constructor(){this.subscriptions=[]}add(e){return We(this.subscriptions,e),()=>Xe(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Ke(t,e){return e?t*(1e3/e):0}const or=t=>!isNaN(parseFloat(t));class rr{constructor(e,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:r,timestamp:o}=W;this.lastUpdated!==o&&(this.timeDelta=r,this.lastUpdated=o,N.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>N.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=or(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){return this.events[e]||(this.events[e]=new He),this.events[e].add(n)}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e){this.passiveEffect=e}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Ke(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function ut(t,e){return new rr(t,e)}const Ye=(t,e)=>n=>!!(At(n)&&so.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Os=(t,e,n)=>s=>{if(!At(s))return s;const[i,r,o,a]=s.match(Tt);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},ar=t=>ct(0,255,t),oe={...st,transform:t=>Math.round(ar(t))},tt={test:Ye("rgb","red"),parse:Os("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+oe.transform(t)+", "+oe.transform(e)+", "+oe.transform(n)+", "+yt(gt.transform(s))+")"};function lr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const be={test:Ye("#"),parse:lr,transform:tt.transform},rt={test:Ye("hsl","hue"),parse:Os("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+U.transform(yt(e))+", "+U.transform(yt(n))+", "+yt(gt.transform(s))+")"},R={test:t=>tt.test(t)||be.test(t)||rt.test(t),parse:t=>tt.test(t)?tt.parse(t):rt.test(t)?rt.parse(t):be.parse(t),transform:t=>At(t)?t:t.hasOwnProperty("red")?tt.transform(t):rt.transform(t)},Is="${c}",_s="${n}";function cr(t){var e,n;return isNaN(t)&&At(t)&&(((e=t.match(Tt))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(pe))===null||n===void 0?void 0:n.length)||0)>0}function _t(t){typeof t=="number"&&(t=`${t}`);const e=[];let n=0,s=0;const i=t.match(pe);i&&(n=i.length,t=t.replace(pe,Is),e.push(...i.map(R.parse)));const r=t.match(Tt);return r&&(s=r.length,t=t.replace(Tt,_s),e.push(...r.map(st.parse))),{values:e,numColors:n,numNumbers:s,tokenised:t}}function Us(t){return _t(t).values}function $s(t){const{values:e,numColors:n,tokenised:s}=_t(t),i=e.length;return r=>{let o=s;for(let a=0;a<i;a++)o=o.replace(a<n?Is:_s,a<n?R.transform(r[a]):yt(r[a]));return o}}const ur=t=>typeof t=="number"?0:t;function fr(t){const e=Us(t);return $s(t)(e.map(ur))}const q={test:cr,parse:Us,createTransformer:$s,getAnimatableNone:fr},dr=new Set(["brightness","contrast","saturate","opacity"]);function hr(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Tt)||[];if(!s)return t;const i=n.replace(s,"");let r=dr.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const mr=/([a-z-]*)\(.*?\)/g,we={...q,getAnimatableNone:t=>{const e=t.match(mr);return e?e.map(hr).join(" "):t}},pr={...ps,color:R,backgroundColor:R,outlineColor:R,fill:R,stroke:R,borderColor:R,borderTopColor:R,borderRightColor:R,borderBottomColor:R,borderLeftColor:R,filter:we,WebkitFilter:we},qe=t=>pr[t];function Ze(t,e){var n;let s=qe(t);return s!==we&&(s=q),(n=s.getAnimatableNone)===null||n===void 0?void 0:n.call(s,e)}const zs=t=>e=>e.test(t),gr={test:t=>t==="auto",parse:t=>t},Gs=[st,T,U,X,oo,io,gr],dt=t=>Gs.find(zs(t)),yr=[...Gs,R,q],vr=t=>yr.find(zs(t));function xr(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function br(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function te(t,e,n){const s=t.getProps();return ze(s,e,n!==void 0?n:s.custom,xr(t),br(t))}function wr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ut(n))}function Vr(t,e){const n=te(t,e);let{transitionEnd:s={},transition:i={},...r}=n?t.makeTargetAnimatable(n,!1):{};r={...r,...s};for(const o in r){const a=Po(r[o]);wr(t,o,a)}}function Tr(t,e,n){var s,i;const r=Object.keys(e).filter(a=>!t.hasValue(a)),o=r.length;if(o)for(let a=0;a<o;a++){const l=r[a],c=e[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(i=(s=n[l])!==null&&s!==void 0?s:t.readValue(l))!==null&&i!==void 0?i:e[l]),u!=null&&(typeof u=="string"&&(Qo(u)||Jo(u))?u=parseFloat(u):!vr(u)&&q.test(c)&&(u=Ze(l,c)),t.addValue(l,ut(u,{owner:t})),n[l]===void 0&&(n[l]=u),u!==null&&t.setBaseTarget(l,u))}}function Pr(t,e){return e?(e[t]||e.default||e).from:void 0}function Ar(t,e,n){var s;const i={};for(const r in t){const o=Pr(r,e);i[r]=o!==void 0?o:(s=n.getValue(r))===null||s===void 0?void 0:s.get()}return i}function Ut(t){return!!($(t)&&t.add)}const Sr=(t,e)=>`${t}: ${e}`;function Cr(t,e){const{MotionAppearAnimations:n}=window,s=Sr(t,nt.has(e)?"transform":e),i=n&&n.get(s);return i?(N.render(()=>{try{i.cancel(),n.delete(s)}catch{}}),i.currentTime||0):0}const Mr="framerAppearId",jr="data-"+Ue(Mr);var $t=function(){};const Et=t=>t*1e3,Dr={current:!1},Qe=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Je=t=>e=>1-t(1-e),tn=t=>t*t,Rr=Je(tn),en=Qe(tn),C=(t,e,n)=>-n*t+n*e+t;function re(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Lr({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=re(l,a,t+1/3),r=re(l,a,t),o=re(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}const ae=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},Nr=[be,tt,rt],Er=t=>Nr.find(e=>e.test(t));function pn(t){const e=Er(t);let n=e.parse(t);return e===rt&&(n=Lr(n)),n}const Ws=(t,e)=>{const n=pn(t),s=pn(e),i={...n};return r=>(i.red=ae(n.red,s.red,r),i.green=ae(n.green,s.green,r),i.blue=ae(n.blue,s.blue,r),i.alpha=C(n.alpha,s.alpha,r),tt.transform(i))};function Xs(t,e){return typeof t=="number"?n=>C(t,e,n):R.test(t)?Ws(t,e):Ks(t,e)}const Hs=(t,e)=>{const n=[...t],s=n.length,i=t.map((r,o)=>Xs(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}},Fr=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Xs(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}},Ks=(t,e)=>{const n=q.createTransformer(e),s=_t(t),i=_t(e);return s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?Qt(Hs(s.values,i.values),n):o=>`${o>0?e:t}`},zt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},gn=(t,e)=>n=>C(t,e,n);function Br(t){return typeof t=="number"?gn:typeof t=="string"?R.test(t)?Ws:Ks:Array.isArray(t)?Hs:typeof t=="object"?Fr:gn}function kr(t,e,n){const s=[],i=n||Br(t[0]),r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const l=Array.isArray(e)?e[o]:e;a=Qt(l,a)}s.push(a)}return s}function Ys(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;$t(r===e.length),$t(!s||!Array.isArray(s)||s.length===r-1),t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=kr(e,s,i),a=o.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const f=zt(t[u],t[u+1],c);return o[u](f)};return n?c=>l(ct(t[0],t[r-1],c)):l}const nn=t=>t,qs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Or=1e-7,Ir=12;function _r(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=qs(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>Or&&++a<Ir);return o}function Zs(t,e,n,s){if(t===e&&n===s)return nn;const i=r=>_r(r,0,1,t,n);return r=>r===0||r===1?r:qs(i(r),e,s)}const Qs=t=>1-Math.sin(Math.acos(t)),sn=Je(Qs),Ur=Qe(sn),Js=Zs(.33,1.53,.69,.99),on=Je(Js),$r=Qe(on),zr=t=>(t*=2)<1?.5*on(t):.5*(2-Math.pow(2,-10*(t-1))),Gr={linear:nn,easeIn:tn,easeInOut:en,easeOut:Rr,circIn:Qs,circInOut:Ur,circOut:sn,backIn:on,backInOut:$r,backOut:Js,anticipate:zr},yn=t=>{if(Array.isArray(t)){$t(t.length===4);const[e,n,s,i]=t;return Zs(e,n,s,i)}else if(typeof t=="string")return Gr[t];return t},Wr=t=>Array.isArray(t)&&typeof t[0]!="number";function Xr(t,e){return t.map(()=>e||en).splice(0,t.length-1)}function Hr(t){const e=t.length;return t.map((n,s)=>s!==0?s/(e-1):0)}function Kr(t,e){return t.map(n=>n*e)}function Gt({keyframes:t,ease:e=en,times:n,duration:s=300}){t=[...t];const i=Gt[0],r=Wr(e)?e.map(yn):yn(e),o={done:!1,value:i},a=Kr(n&&n.length===Gt.length?n:Hr(t),s);function l(){return Ys(a,t,{ease:Array.isArray(r)?r:Xr(t,r)})}let c=l();return{next:u=>(o.value=c(u),o.done=u>=s,o),flipTarget:()=>{t.reverse(),c=l()}}}const le=.001,Yr=.01,qr=10,Zr=.05,Qr=1;function Jr({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,r,o=1-e;o=ct(Zr,Qr,o),t=ct(Yr,qr,t/1e3),o<1?(i=c=>{const u=c*o,f=u*t,d=u-n,h=Ve(c,o),p=Math.exp(-f);return le-d/h*p},r=c=>{const f=c*o*t,d=f*n+n,h=Math.pow(o,2)*Math.pow(c,2)*t,p=Math.exp(-f),g=Ve(Math.pow(c,2),o);return(-i(c)+le>0?-1:1)*((d-h)*p)/g}):(i=c=>{const u=Math.exp(-c*t),f=(c-n)*t+1;return-le+u*f},r=c=>{const u=Math.exp(-c*t),f=(n-c)*(t*t);return u*f});const a=5/t,l=ea(i,r,a);if(t=t*1e3,isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:o*2*Math.sqrt(s*c),duration:t}}}const ta=12;function ea(t,e,n){let s=n;for(let i=1;i<ta;i++)s=s-t(s)/e(s);return s}function Ve(t,e){return t*Math.sqrt(1-e*e)}const na=["duration","bounce"],sa=["stiffness","damping","mass"];function vn(t,e){return e.some(n=>t[n]!==void 0)}function ia(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!vn(t,sa)&&vn(t,na)){const n=Jr(t);e={...e,...n,velocity:0,mass:1},e.isResolvedFromDuration=!0}return e}const oa=5;function ti({keyframes:t,restSpeed:e=2,restDelta:n=.01,...s}){let i=t[0],r=t[t.length-1];const o={done:!1,value:i},{stiffness:a,damping:l,mass:c,velocity:u,duration:f,isResolvedFromDuration:d}=ia(s);let h=ra,p=u?-(u/1e3):0;const g=l/(2*Math.sqrt(a*c));function w(){const v=r-i,V=Math.sqrt(a/c)/1e3;if(n===void 0&&(n=Math.min(Math.abs(r-i)/100,.4)),g<1){const x=Ve(V,g);h=b=>{const P=Math.exp(-g*V*b);return r-P*((p+g*V*v)/x*Math.sin(x*b)+v*Math.cos(x*b))}}else if(g===1)h=x=>r-Math.exp(-V*x)*(v+(p+V*v)*x);else{const x=V*Math.sqrt(g*g-1);h=b=>{const P=Math.exp(-g*V*b),S=Math.min(x*b,300);return r-P*((p+g*V*v)*Math.sinh(S)+x*v*Math.cosh(S))/x}}}return w(),{next:v=>{const V=h(v);if(d)o.done=v>=f;else{let x=p;if(v!==0)if(g<1){const S=Math.max(0,v-oa);x=Ke(V-h(S),v-S)}else x=0;const b=Math.abs(x)<=e,P=Math.abs(r-V)<=n;o.done=b&&P}return o.value=o.done?r:V,o},flipTarget:()=>{p=-p,[i,r]=[r,i],w()}}}ti.needsInterpolation=(t,e)=>typeof t=="string"||typeof e=="string";const ra=t=>0;function aa({keyframes:t=[0],velocity:e=0,power:n=.8,timeConstant:s=350,restDelta:i=.5,modifyTarget:r}){const o=t[0],a={done:!1,value:o};let l=n*e;const c=o+l,u=r===void 0?c:r(c);return u!==c&&(l=u-o),{next:f=>{const d=-l*Math.exp(-f/s);return a.done=!(d>i||d<-i),a.value=a.done?u:u+d,a},flipTarget:()=>{}}}const la={decay:aa,keyframes:Gt,tween:Gt,spring:ti};function ei(t,e,n=0){return t-e-n}function ca(t,e=0,n=0,s=!0){return s?ei(e+-t,e,n):e-(t-e)+n}function ua(t,e,n,s){return s?t>=e+n:t<=-n}const fa=t=>{const e=({delta:n})=>t(n);return{start:()=>N.update(e,!0),stop:()=>Y.update(e)}};function Wt({duration:t,driver:e=fa,elapsed:n=0,repeat:s=0,repeatType:i="loop",repeatDelay:r=0,keyframes:o,autoplay:a=!0,onPlay:l,onStop:c,onComplete:u,onRepeat:f,onUpdate:d,type:h="keyframes",...p}){var g,w;let v,V=0,x=t,b,P=!1,S=!0,L;const O=la[o.length>2?"keyframes":h],F=o[0],D=o[o.length-1];!((w=(g=O).needsInterpolation)===null||w===void 0)&&w.call(g,F,D)&&(L=Ys([0,100],[F,D],{clamp:!1}),o=[0,100]);const z=O({...p,duration:t,keyframes:o});function Mt(){V++,i==="reverse"?(S=V%2===0,n=ca(n,x,r,S)):(n=ei(n,x,r),i==="mirror"&&z.flipTarget()),P=!1,f&&f()}function ee(){v.stop(),u&&u()}function ft(B){if(S||(B=-B),n+=B,!P){const it=z.next(Math.max(0,n));b=it.value,L&&(b=L(b)),P=S?it.done:n<=0}d&&d(b),P&&(V===0&&(x=x!==void 0?x:n),V<s?ua(n,x,r,S)&&Mt():ee())}function j(){l&&l(),v=e(ft),v.start()}return a&&j(),{stop:()=>{c&&c(),v.stop()},sample:B=>z.next(Math.max(0,B))}}function da(t){return!t||Array.isArray(t)||typeof t=="string"&&ni[t]}const mt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,ni={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:mt([0,.65,.55,1]),circOut:mt([.55,0,1,.45]),backIn:mt([.31,.01,.66,-.59]),backOut:mt([.33,1.53,.69,.99])};function ha(t){if(t)return Array.isArray(t)?mt(t):ni[t]}function ma(t,e,n,{delay:s=0,duration:i,repeat:r=0,repeatType:o="loop",ease:a,times:l}={}){return t.animate({[e]:n,offset:l},{delay:s,duration:i,easing:ha(a),fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}const Dt=10;function pa(t,e,{onUpdate:n,onComplete:s,...i}){let{keyframes:r,duration:o=.3,elapsed:a=0,ease:l}=i;if(i.type==="spring"||!da(i.ease)){const u=Wt(i);let f={done:!1,value:r[0]};const d=[];let h=0;for(;!f.done;)f=u.sample(h),d.push(f.value),h+=Dt;r=d,o=h-Dt,l="linear"}const c=ma(t.owner.current,e,r,{...i,delay:-a,duration:o,ease:l});return c.onfinish=()=>{t.set(r[r.length-1]),s&&s()},()=>{const{currentTime:u}=c;if(u){const f=Wt(i);t.setWithVelocity(f.sample(u-Dt).value,f.sample(u).value,Dt)}N.update(()=>c.cancel())}}function si(t,e){const n=performance.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(Y.read(s),t(r-e))};return N.read(s,!0),()=>Y.read(s)}function ga({keyframes:t,elapsed:e,onUpdate:n,onComplete:s}){const i=()=>(n&&n(t[t.length-1]),s&&s(),()=>{});return e?si(i,-e):i()}function ya({keyframes:t,velocity:e=0,min:n,max:s,power:i=.8,timeConstant:r=750,bounceStiffness:o=500,bounceDamping:a=10,restDelta:l=1,modifyTarget:c,driver:u,onUpdate:f,onComplete:d,onStop:h}){const p=t[0];let g;function w(b){return n!==void 0&&b<n||s!==void 0&&b>s}function v(b){return n===void 0?s:s===void 0||Math.abs(n-b)<Math.abs(s-b)?n:s}function V(b){g==null||g.stop(),g=Wt({keyframes:[0,1],velocity:0,...b,driver:u,onUpdate:P=>{var S;f==null||f(P),(S=b.onUpdate)===null||S===void 0||S.call(b,P)},onComplete:d,onStop:h})}function x(b){V({type:"spring",stiffness:o,damping:a,restDelta:l,...b})}if(w(p))x({velocity:e,keyframes:[p,v(p)]});else{let b=i*e+p;typeof c<"u"&&(b=c(b));const P=v(b),S=P===n?-1:1;let L,O;const F=D=>{L=O,O=D,e=Ke(D-L,W.delta),(S===1&&D>P||S===-1&&D<P)&&x({keyframes:[D,P],velocity:e})};V({type:"decay",keyframes:[p,0],velocity:e,timeConstant:r,power:i,restDelta:l,modifyTarget:c,onUpdate:w(b)?F:void 0})}return{stop:()=>g==null?void 0:g.stop()}}const Z=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Rt=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),ce=()=>({type:"keyframes",ease:"linear",duration:.3}),va={type:"keyframes",duration:.8},xn={x:Z,y:Z,z:Z,rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scaleX:Rt,scaleY:Rt,scale:Rt,opacity:ce,backgroundColor:ce,color:ce,default:Rt},xa=(t,{keyframes:e})=>e.length>2?va:(xn[t]||xn.default)(e[1]),Te=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&q.test(e)&&!e.startsWith("url("));function ba({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,...c}){return!!Object.keys(c).length}function bn(t){return t===0||typeof t=="string"&&parseFloat(t)===0&&t.indexOf(" ")===-1}function wn(t){return typeof t=="number"?0:Ze("",t)}function ii(t,e){return t[e]||t.default||t}function wa(t,e,n,s){const i=Te(e,n);let r=s.from!==void 0?s.from:t.get();return r==="none"&&i&&typeof n=="string"?r=Ze(e,n):bn(r)&&typeof n=="string"?r=wn(n):!Array.isArray(n)&&bn(n)&&typeof r=="string"&&(n=wn(r)),Array.isArray(n)?(n[0]===null&&(n[0]=r),n):[r,n]}const Vn={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},ue={},oi={};for(const t in Vn)oi[t]=()=>(ue[t]===void 0&&(ue[t]=Vn[t]()),ue[t]);const Va=new Set(["opacity"]),rn=(t,e,n,s={})=>i=>{const r=ii(s,t)||{},o=r.delay||s.delay||0;let{elapsed:a=0}=s;a=a-Et(o);const l=wa(e,t,n,r),c=l[0],u=l[l.length-1],f=Te(t,c),d=Te(t,u);let h={keyframes:l,velocity:e.getVelocity(),...r,elapsed:a,onUpdate:v=>{e.set(v),r.onUpdate&&r.onUpdate(v)},onComplete:()=>{i(),r.onComplete&&r.onComplete()}};if(!f||!d||Dr.current||r.type===!1)return ga(h);if(r.type==="inertia"){const v=ya(h);return()=>v.stop()}ba(r)||(h={...h,...xa(t,h)}),h.duration&&(h.duration=Et(h.duration)),h.repeatDelay&&(h.repeatDelay=Et(h.repeatDelay));const p=e.owner,g=p&&p.current;if(oi.waapi()&&Va.has(t)&&!h.repeatDelay&&h.repeatType!=="mirror"&&h.damping!==0&&p&&g instanceof HTMLElement&&!p.getProps().onUpdate)return pa(e,t,h);{const v=Wt(h);return()=>v.stop()}};function Ta(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Pe(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Pe(t,e,n);else{const i=typeof e=="function"?te(t,e,n.custom):e;s=ri(t,i,n)}return s.then(()=>t.notify("AnimationComplete",e))}function Pe(t,e,n={}){var s;const i=te(t,e,n.custom);let{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);const o=i?()=>ri(t,i,n):()=>Promise.resolve(),a=!((s=t.variantChildren)===null||s===void 0)&&s.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:f,staggerDirection:d}=r;return Pa(t,e,u+c,f,d,n)}:()=>Promise.resolve(),{when:l}=r;if(l){const[c,u]=l==="beforeChildren"?[o,a]:[a,o];return c().then(u)}else return Promise.all([o(),a(n.delay)])}function ri(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=t.makeTargetAnimatable(e);const c=t.getValue("willChange");s&&(o=s);const u=[],f=i&&((r=t.animationState)===null||r===void 0?void 0:r.getState()[i]);for(const d in l){const h=t.getValue(d),p=l[d];if(!h||p===void 0||f&&Sa(f,d))continue;let g={delay:n,elapsed:0,...o};if(t.shouldReduceMotion&&nt.has(d)&&(g={...g,type:!1,delay:0}),!h.hasAnimated){const v=t.getProps()[jr];v&&(g.elapsed=Cr(v,d))}let w=h.start(rn(d,h,p,g));Ut(c)&&(c.add(d),w=w.then(()=>c.remove(d))),u.push(w)}return Promise.all(u).then(()=>{a&&Vr(t,a)})}function Pa(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(Aa).forEach((c,u)=>{o.push(Pe(c,e,{...r,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(o)}function Aa(t,e){return t.sortNodePosition(e)}function Sa({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}const an=[A.Animate,A.InView,A.Focus,A.Hover,A.Tap,A.Drag,A.Exit],Ca=[...an].reverse(),Ma=an.length;function ja(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Ta(t,n,s)))}function Da(t){let e=ja(t);const n=La();let s=!0;const i=(l,c)=>{const u=te(t,c);if(u){const{transition:f,transitionEnd:d,...h}=u;l={...l,...h,...d}}return l};function r(l){e=l(t)}function o(l,c){const u=t.getProps(),f=t.getVariantContext(!0)||{},d=[],h=new Set;let p={},g=1/0;for(let v=0;v<Ma;v++){const V=Ca[v],x=n[V],b=u[V]!==void 0?u[V]:f[V],P=wt(b),S=V===c?x.isActive:null;S===!1&&(g=v);let L=b===f[V]&&b!==u[V]&&P;if(L&&s&&t.manuallyAnimateOnMount&&(L=!1),x.protectedKeys={...p},!x.isActive&&S===null||!b&&!x.prevProp||Yt(b)||typeof b=="boolean")continue;const O=Ra(x.prevProp,b);let F=O||V===c&&x.isActive&&!L&&P||v>g&&P;const D=Array.isArray(b)?b:[b];let z=D.reduce(i,{});S===!1&&(z={});const{prevResolvedValues:Mt={}}=x,ee={...Mt,...z},ft=j=>{F=!0,h.delete(j),x.needsAnimating[j]=!0};for(const j in ee){const B=z[j],it=Mt[j];p.hasOwnProperty(j)||(B!==it?Ot(B)&&Ot(it)?!Es(B,it)||O?ft(j):x.protectedKeys[j]=!0:B!==void 0?ft(j):h.add(j):B!==void 0&&h.has(j)?ft(j):x.protectedKeys[j]=!0)}x.prevProp=b,x.prevResolvedValues=z,x.isActive&&(p={...p,...z}),s&&t.blockInitialAnimation&&(F=!1),F&&!L&&d.push(...D.map(j=>({animation:j,options:{type:V,...l}})))}if(h.size){const v={};h.forEach(V=>{const x=t.getBaseTarget(V);x!==void 0&&(v[V]=x)}),d.push({animation:v})}let w=!!d.length;return s&&u.initial===!1&&!t.manuallyAnimateOnMount&&(w=!1),s=!1,w?e(d):Promise.resolve()}function a(l,c,u){var f;if(n[l].isActive===c)return Promise.resolve();(f=t.variantChildren)===null||f===void 0||f.forEach(h=>{var p;return(p=h.animationState)===null||p===void 0?void 0:p.setActive(l,c)}),n[l].isActive=c;const d=o(u,l);for(const h in n)n[h].protectedKeys={};return d}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n}}function Ra(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Es(e,t):!1}function Q(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function La(){return{[A.Animate]:Q(!0),[A.InView]:Q(),[A.Hover]:Q(),[A.Tap]:Q(),[A.Drag]:Q(),[A.Focus]:Q(),[A.Exit]:Q()}}const Na={animation:K(({visualElement:t,animate:e})=>{t.animationState||(t.animationState=Da(t)),Yt(e)&&y.useEffect(()=>e.subscribe(t),[e])}),exit:K(t=>{const{custom:e,visualElement:n}=t,[s,i]=Ns(),r=y.useContext(Kt);y.useEffect(()=>{n.isPresent=s;const o=n.animationState&&n.animationState.setActive(A.Exit,!s,{custom:r&&r.custom||e});o&&!s&&o.then(i)},[s])})},Tn=(t,e)=>Math.abs(t-e);function Ea(t,e){const n=Tn(t.x,e.x),s=Tn(t.y,e.y);return Math.sqrt(n**2+s**2)}class ai{constructor(e,n,{transformPagePoint:s}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=de(this.lastMoveEventInfo,this.history),u=this.startEvent!==null,f=Ea(c.offset,{x:0,y:0})>=3;if(!u&&!f)return;const{point:d}=c,{timestamp:h}=W;this.history.push({...d,timestamp:h});const{onStart:p,onMove:g}=this.handlers;u||(p&&p(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,c)},this.handlePointerMove=(c,u)=>{if(this.lastMoveEvent=c,this.lastMoveEventInfo=fe(u,this.transformPagePoint),Ps(c)&&c.buttons===0){this.handlePointerUp(c,u);return}N.update(this.updatePoint,!0)},this.handlePointerUp=(c,u)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,h=de(fe(u,this.transformPagePoint),this.history);this.startEvent&&f&&f(c,h),d&&d(c,h)},As(e)&&e.touches.length>1)return;this.handlers=n,this.transformPagePoint=s;const i=Ge(e),r=fe(i,this.transformPagePoint),{point:o}=r,{timestamp:a}=W;this.history=[{...o,timestamp:a}];const{onSessionStart:l}=n;l&&l(e,de(r,this.history)),this.removeListeners=Qt(lt(window,"pointermove",this.handlePointerMove),lt(window,"pointerup",this.handlePointerUp),lt(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Y.update(this.updatePoint)}}function fe(t,e){return e?{point:e(t.point)}:t}function Pn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function de({point:t},e){return{point:t,delta:Pn(t,li(e)),offset:Pn(t,Fa(e)),velocity:Ba(e,.1)}}function Fa(t){return t[0]}function li(t){return t[t.length-1]}function Ba(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=li(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>Et(e)));)n--;if(!s)return{x:0,y:0};const r=(i.timestamp-s.timestamp)/1e3;if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function E(t){return t.max-t.min}function Ae(t,e=0,n=.01){return Math.abs(t-e)<=n}function An(t,e,n,s=.5){t.origin=s,t.originPoint=C(e.min,e.max,t.origin),t.scale=E(n)/E(e),(Ae(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=C(n.min,n.max,t.origin)-t.originPoint,(Ae(t.translate)||isNaN(t.translate))&&(t.translate=0)}function vt(t,e,n,s){An(t.x,e.x,n.x,s==null?void 0:s.originX),An(t.y,e.y,n.y,s==null?void 0:s.originY)}function Sn(t,e,n){t.min=n.min+e.min,t.max=t.min+E(e)}function ka(t,e,n){Sn(t.x,e.x,n.x),Sn(t.y,e.y,n.y)}function Cn(t,e,n){t.min=e.min-n.min,t.max=t.min+E(e)}function xt(t,e,n){Cn(t.x,e.x,n.x),Cn(t.y,e.y,n.y)}function Oa(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?C(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?C(n,t,s.max):Math.min(t,n)),t}function Mn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Ia(t,{top:e,left:n,bottom:s,right:i}){return{x:Mn(t.x,n,i),y:Mn(t.y,e,s)}}function jn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function _a(t,e){return{x:jn(t.x,e.x),y:jn(t.y,e.y)}}function Ua(t,e){let n=.5;const s=E(t),i=E(e);return i>s?n=zt(e.min,e.max-s,t.min):s>i&&(n=zt(t.min,t.max-i,e.min)),ct(0,1,n)}function $a(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Se=.35;function za(t=Se){return t===!1?t=0:t===!0&&(t=Se),{x:Dn(t,"left","right"),y:Dn(t,"top","bottom")}}function Dn(t,e,n){return{min:Rn(t,e),max:Rn(t,n)}}function Rn(t,e){return typeof t=="number"?t:t[e]||0}const Ln=()=>({translate:0,scale:1,origin:0,originPoint:0}),bt=()=>({x:Ln(),y:Ln()}),Nn=()=>({min:0,max:0}),M=()=>({x:Nn(),y:Nn()});function I(t){return[t("x"),t("y")]}function ci({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Ga({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Wa(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function he(t){return t===void 0||t===1}function Ce({scale:t,scaleX:e,scaleY:n}){return!he(t)||!he(e)||!he(n)}function J(t){return Ce(t)||ui(t)||t.z||t.rotate||t.rotateX||t.rotateY}function ui(t){return En(t.x)||En(t.y)}function En(t){return t&&t!=="0%"}function Xt(t,e,n){const s=t-n,i=e*s;return n+i}function Fn(t,e,n,s,i){return i!==void 0&&(t=Xt(t,i,s)),Xt(t,n,s)+e}function Me(t,e=0,n=1,s,i){t.min=Fn(t.min,e,n,s,i),t.max=Fn(t.max,e,n,s,i)}function fi(t,{x:e,y:n}){Me(t.x,e.translate,e.scale,e.originPoint),Me(t.y,n.translate,n.scale,n.originPoint)}function Xa(t,e,n,s=!1){var i,r;const o=n.length;if(!o)return;e.x=e.y=1;let a,l;for(let c=0;c<o;c++)a=n[c],l=a.projectionDelta,((r=(i=a.instance)===null||i===void 0?void 0:i.style)===null||r===void 0?void 0:r.display)!=="contents"&&(s&&a.options.layoutScroll&&a.scroll&&a!==a.root&&at(t,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),l&&(e.x*=l.x.scale,e.y*=l.y.scale,fi(t,l)),s&&J(a.latestValues)&&at(t,a.latestValues));e.x=Bn(e.x),e.y=Bn(e.y)}function Bn(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function H(t,e){t.min=t.min+e,t.max=t.max+e}function kn(t,e,[n,s,i]){const r=e[i]!==void 0?e[i]:.5,o=C(t.min,t.max,r);Me(t,e[n],e[s],o,e.scale)}const Ha=["x","scaleX","originX"],Ka=["y","scaleY","originY"];function at(t,e){kn(t.x,e,Ha),kn(t.y,e,Ka)}function di(t,e){return ci(Wa(t.getBoundingClientRect(),e))}function Ya(t,e,n){const s=di(t,n),{scroll:i}=e;return i&&(H(s.x,i.offset.x),H(s.y,i.offset.y)),s}const qa=new WeakMap;class Za{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=M(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const s=a=>{this.stopAnimation(),n&&this.snapToCursor(Ge(a,"page").point)},i=(a,l)=>{var c;const{drag:u,dragPropagation:f,onDragStart:d}=this.getProps();u&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=js(u),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),I(h=>{var p,g;let w=this.getAxisMotionValue(h).get()||0;if(U.test(w)){const v=(g=(p=this.visualElement.projection)===null||p===void 0?void 0:p.layout)===null||g===void 0?void 0:g.layoutBox[h];v&&(w=E(v)*(parseFloat(w)/100))}this.originPoint[h]=w}),d==null||d(a,l),(c=this.visualElement.animationState)===null||c===void 0||c.setActive(A.Drag,!0))},r=(a,l)=>{const{dragPropagation:c,dragDirectionLock:u,onDirectionLock:f,onDrag:d}=this.getProps();if(!c&&!this.openGlobalLock)return;const{offset:h}=l;if(u&&this.currentDirection===null){this.currentDirection=Qa(h),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",l.point,h),this.updateAxis("y",l.point,h),this.visualElement.render(),d==null||d(a,l)},o=(a,l)=>this.stop(a,l);this.panSession=new ai(e,{onSessionStart:s,onStart:i,onMove:r,onSessionEnd:o},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r==null||r(e,n)}cancel(){var e,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(e=this.panSession)===null||e===void 0||e.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(A.Drag,!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Lt(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=Oa(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){const{dragConstraints:e,dragElastic:n}=this.getProps(),{layout:s}=this.visualElement.projection||{},i=this.constraints;e&&ot(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Ia(s.layoutBox,e):this.constraints=!1,this.elastic=za(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&I(r=>{this.getAxisMotionValue(r)&&(this.constraints[r]=$a(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ot(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=Ya(s,i.root,this.visualElement.getTransformPagePoint());let o=_a(i.layout.layoutBox,r);if(n){const a=n(Ga(o));this.hasMutatedConstraints=!!a,a&&(o=ci(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=I(u=>{if(!Lt(u,n,this.currentDirection))return;let f=(l==null?void 0:l[u])||{};o&&(f={min:0,max:0});const d=i?200:1e6,h=i?40:1e7,p={type:"inertia",velocity:s?e[u]:0,bounceStiffness:d,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...r,...f};return this.startAxisValueAnimation(u,p)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(rn(e,s,0,n))}stopAnimation(){I(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){var n;const s="_drag"+e.toUpperCase(),i=this.visualElement.getProps()[s];return i||this.visualElement.getValue(e,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[e])||0)}snapToCursor(e){I(n=>{const{drag:s}=this.getProps();if(!Lt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-C(o,a,.5))}})}scalePositionWithinConstraints(){var e;if(!this.visualElement.current)return;const{drag:n,dragConstraints:s}=this.getProps(),{projection:i}=this.visualElement;if(!ot(s)||!i||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};I(a=>{const l=this.getAxisMotionValue(a);if(l){const c=l.get();r[a]=Ua({min:c,max:c},this.constraints[a])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",(e=i.root)===null||e===void 0||e.updateScroll(),i.updateLayout(),this.resolveConstraints(),I(a=>{if(!Lt(a,n,null))return;const l=this.getAxisMotionValue(a),{min:c,max:u}=this.constraints[a];l.set(C(c,u,r[a]))})}addListeners(){var e;if(!this.visualElement.current)return;qa.set(this.visualElement,this);const n=this.visualElement.current,s=lt(n,"pointerdown",c=>{const{drag:u,dragListener:f=!0}=this.getProps();u&&f&&this.start(c)}),i=()=>{const{dragConstraints:c}=this.getProps();ot(c)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",i);r&&!r.layout&&((e=r.root)===null||e===void 0||e.updateScroll(),r.updateLayout()),i();const a=Zt(window,"resize",()=>this.scalePositionWithinConstraints()),l=r.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(I(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=c[f].translate,d.set(d.get()+c[f].translate))}),this.visualElement.render())});return()=>{a(),s(),o(),l==null||l()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Se,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Lt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Qa(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}function Ja(t){const{dragControls:e,visualElement:n}=t,s=Fe(()=>new Za(n));y.useEffect(()=>e&&e.subscribe(s),[s,e]),y.useEffect(()=>s.addListeners(),[s])}function tl({onPan:t,onPanStart:e,onPanEnd:n,onPanSessionStart:s,visualElement:i}){const r=t||e||n||s,o=y.useRef(null),{transformPagePoint:a}=y.useContext(Ee),l={onSessionStart:s,onStart:e,onMove:t,onEnd:(u,f)=>{o.current=null,n&&n(u,f)}};y.useEffect(()=>{o.current!==null&&o.current.updateHandlers(l)});function c(u){o.current=new ai(u,l,{transformPagePoint:a})}It(i,"pointerdown",r&&c),Ls(()=>o.current&&o.current.end())}const el={pan:K(tl),drag:K(Ja)};function je(t){return typeof t=="string"&&t.startsWith("var(--")}const hi=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nl(t){const e=hi.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function De(t,e,n=1){const[s,i]=nl(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);return r?r.trim():je(i)?De(i,e,n+1):i}function sl(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const r=i.get();if(!je(r))return;const o=De(r,s);o&&i.set(o)});for(const i in e){const r=e[i];if(!je(r))continue;const o=De(r,s);o&&(e[i]=o,n&&n[i]===void 0&&(n[i]=r))}return{target:e,transitionEnd:n}}const il=new Set(["width","height","top","left","right","bottom","x","y"]),mi=t=>il.has(t),ol=t=>Object.keys(t).some(mi),pi=(t,e)=>{t.set(e,!1),t.set(e)},On=t=>t===st||t===T;var In;(function(t){t.width="width",t.height="height",t.left="left",t.right="right",t.top="top",t.bottom="bottom"})(In||(In={}));const _n=(t,e)=>parseFloat(t.split(", ")[e]),Un=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return _n(i[1],e);{const r=s.match(/^matrix\((.+)\)$/);return r?_n(r[1],t):0}},rl=new Set(["x","y","z"]),al=Bt.filter(t=>!rl.has(t));function ll(t){const e=[];return al.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const $n={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Un(4,13),y:Un(5,14)},cl=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,r=getComputedStyle(i),{display:o}=r,a={};o==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(c=>{a[c]=$n[c](s,r)}),e.render();const l=e.measureViewportBox();return n.forEach(c=>{const u=e.getValue(c);pi(u,a[c]),t[c]=$n[c](l,r)}),t},ul=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(mi);let r=[],o=!1;const a=[];if(i.forEach(l=>{const c=t.getValue(l);if(!t.hasValue(l))return;let u=n[l],f=dt(u);const d=e[l];let h;if(Ot(d)){const p=d.length,g=d[0]===null?1:0;u=d[g],f=dt(u);for(let w=g;w<p;w++)h?$t(dt(d[w])===h):h=dt(d[w])}else h=dt(d);if(f!==h)if(On(f)&&On(h)){const p=c.get();typeof p=="string"&&c.set(parseFloat(p)),typeof d=="string"?e[l]=parseFloat(d):Array.isArray(d)&&h===T&&(e[l]=d.map(parseFloat))}else f!=null&&f.transform&&(h!=null&&h.transform)&&(u===0||d===0)?u===0?c.set(h.transform(u)):e[l]=f.transform(d):(o||(r=ll(t),o=!0),a.push(l),s[l]=s[l]!==void 0?s[l]:e[l],pi(c,d))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=cl(e,t,a);return r.length&&r.forEach(([u,f])=>{t.getValue(u).set(f)}),t.render(),et&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:s}}else return{target:e,transitionEnd:s}};function fl(t,e,n,s){return ol(e)?ul(t,e,n,s):{target:e,transitionEnd:s}}const dl=(t,e,n,s)=>{const i=sl(t,e,s);return e=i.target,s=i.transitionEnd,fl(t,e,n,s)},Re={current:null},gi={current:!1};function hl(){if(gi.current=!0,!!et)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Re.current=t.matches;t.addListener(e),e()}else Re.current=!1}function ml(t,e,n){const{willChange:s}=e;for(const i in e){const r=e[i],o=n[i];if($(r))t.addValue(i,r),Ut(s)&&s.add(i);else if($(o))t.addValue(i,ut(r,{owner:t})),Ut(s)&&s.remove(i);else if(o!==r)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(r)}else{const a=t.getStaticValue(i);t.addValue(i,ut(a!==void 0?a:r))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const yi=Object.keys(Vt),pl=yi.length,zn=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class gl{constructor({parent:e,props:n,reducedMotionConfig:s,visualState:i},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>N.render(this.render,!1,!0);const{latestValues:o,renderState:a}=i;this.latestValues=o,this.baseTarget={...o},this.initialValues=n.initial?{...o}:{},this.renderState=a,this.parent=e,this.props=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=r,this.isControllingVariants=qt(n),this.isVariantNode=us(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...c}=this.scrapeMotionValuesFromProps(n);for(const u in c){const f=c[u];o[u]!==void 0&&$(f)&&(f.set(o[u],!1),Ut(l)&&l.add(u))}}scrapeMotionValuesFromProps(e){return{}}mount(e){var n;this.current=e,this.projection&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((s,i)=>this.bindToMotionValue(i,s)),gi.current||hl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Re.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var e,n,s;(e=this.projection)===null||e===void 0||e.unmount(),Y.update(this.notifyUpdate),Y.render(this.render),this.valueSubscriptions.forEach(i=>i()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(s=this.parent)===null||s===void 0||s.children.delete(this);for(const i in this.events)this.events[i].clear();this.current=null}bindToMotionValue(e,n){const s=nt.has(e),i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&N.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),r()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures(e,n,s,i,r,o){const a=[];for(let l=0;l<pl;l++){const c=yi[l],{isEnabled:u,Component:f}=Vt[c];u(e)&&f&&a.push(y.createElement(f,{key:c,...e,visualElement:this}))}if(!this.projection&&r){this.projection=new r(i,this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:f,layoutScroll:d}=e;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||f&&ot(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:d})}return a}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):M()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}setProps(e){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=e;for(let n=0;n<zn.length;n++){const s=zn[n];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i=e["on"+s];i&&(this.propEventSubscriptions[s]=this.on(s,i))}this.prevMotionValues=ml(this,this.scrapeMotionValuesFromProps(e),this.prevMotionValues)}getProps(){return this.props}getVariant(e){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[e]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var e;return this.isVariantNode?this:(e=this.parent)===null||e===void 0?void 0:e.getClosestVariantNode()}getVariantContext(e=!1){var n,s;if(e)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const r=((s=this.parent)===null||s===void 0?void 0:s.getVariantContext())||{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const i={};for(let r=0;r<yl;r++){const o=vi[r],a=this.props[o];(wt(a)||a===!1)&&(i[o]=a)}return i}addVariantChild(e){var n;const s=this.getClosestVariantNode();if(s)return(n=s.variantChildren)===null||n===void 0||n.add(e),()=>s.variantChildren.delete(e)}addValue(e,n){this.hasValue(e)&&this.removeValue(e),this.values.set(e,n),this.latestValues[e]=n.get(),this.bindToMotionValue(e,n)}removeValue(e){var n;this.values.delete(e),(n=this.valueSubscriptions.get(e))===null||n===void 0||n(),this.valueSubscriptions.delete(e),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ut(n,{owner:this}),this.addValue(e,s)),s}readValue(e){return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=ze(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!$(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new He),this.events[e].add(n)}notify(e,...n){var s;(s=this.events[e])===null||s===void 0||s.notify(...n)}}const vi=["initial",...an],yl=vi.length;class xi extends gl{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){var s;return(s=e.style)===null||s===void 0?void 0:s[n]}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},r){let o=Ar(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),o&&(o=i(o))),r){Tr(this,s,o);const a=dl(this,s,o,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function vl(t){return window.getComputedStyle(t)}class xl extends xi{readValueFromInstance(e,n){if(nt.has(n)){const s=qe(n);return s&&s.default||0}else{const s=vl(e),i=(ms(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return di(e,n)}build(e,n,s,i){ke(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e){return $e(e)}renderInstance(e,n,s,i){xs(e,n,s,i)}}class bl extends xi{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){var s;return nt.has(n)?((s=qe(n))===null||s===void 0?void 0:s.default)||0:(n=bs.has(n)?n:Ue(n),e.getAttribute(n))}measureInstanceViewportBox(){return M()}scrapeMotionValuesFromProps(e){return Vs(e)}build(e,n,s,i){Ie(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){ws(e,n,s,i)}mount(e){this.isSVGTag=_e(e.tagName),super.mount(e)}}const wl=(t,e)=>Be(t)?new bl(e,{enableHardwareAcceleration:!1}):new xl(e,{enableHardwareAcceleration:!0});function Gn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const ht={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=Gn(t,e.target.x),s=Gn(t,e.target.y);return`${n}% ${s}%`}},Wn="_$css",Vl={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=t.includes("var("),r=[];i&&(t=t.replace(hi,h=>(r.push(h),Wn)));const o=q.parse(t);if(o.length>5)return s;const a=q.createTransformer(t),l=typeof o[0]!="number"?1:0,c=n.x.scale*e.x,u=n.y.scale*e.y;o[0+l]/=c,o[1+l]/=u;const f=C(c,u,.5);typeof o[2+l]=="number"&&(o[2+l]/=f),typeof o[3+l]=="number"&&(o[3+l]/=f);let d=a(o);if(i){let h=0;d=d.replace(Wn,()=>{const p=r[h];return h++,p})}return d}};class Tl extends Le.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Qi(Al),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),pt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,o=s.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||N.postRender(()=>{var a;!((a=o.getStack())===null||a===void 0)&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),!e.currentAnimation&&e.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(i),s!=null&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e==null||e()}render(){return null}}function Pl(t){const[e,n]=Ns(),s=y.useContext(fs);return Le.createElement(Tl,{...t,layoutGroup:s,switchLayoutGroup:y.useContext(ds),isPresent:e,safeToRemove:n})}const Al={borderRadius:{...ht,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ht,borderTopRightRadius:ht,borderBottomLeftRadius:ht,borderBottomRightRadius:ht,boxShadow:Vl},Sl={measureLayout:Pl};function Cl(t,e,n={}){const s=$(t)?t:ut(t);return s.start(rn("",s,e,n)),{stop:()=>s.stop(),isAnimating:()=>s.isAnimating()}}const bi=["TopLeft","TopRight","BottomLeft","BottomRight"],Ml=bi.length,Xn=t=>typeof t=="string"?parseFloat(t):t,Hn=t=>typeof t=="number"||T.test(t);function jl(t,e,n,s,i,r){i?(t.opacity=C(0,n.opacity!==void 0?n.opacity:1,Dl(s)),t.opacityExit=C(e.opacity!==void 0?e.opacity:1,0,Rl(s))):r&&(t.opacity=C(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let o=0;o<Ml;o++){const a=`border${bi[o]}Radius`;let l=Kn(e,a),c=Kn(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Hn(l)===Hn(c)?(t[a]=Math.max(C(Xn(l),Xn(c),s),0),(U.test(c)||U.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=C(e.rotate||0,n.rotate||0,s))}function Kn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Dl=wi(0,.5,sn),Rl=wi(.5,.95,nn);function wi(t,e,n){return s=>s<t?0:s>e?1:n(zt(t,e,s))}function Yn(t,e){t.min=e.min,t.max=e.max}function k(t,e){Yn(t.x,e.x),Yn(t.y,e.y)}function qn(t,e,n,s,i){return t-=e,t=Xt(t,1/n,s),i!==void 0&&(t=Xt(t,1/i,s)),t}function Ll(t,e=0,n=1,s=.5,i,r=t,o=t){if(U.test(e)&&(e=parseFloat(e),e=C(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=C(r.min,r.max,s);t===r&&(a-=e),t.min=qn(t.min,e,n,a,i),t.max=qn(t.max,e,n,a,i)}function Zn(t,e,[n,s,i],r,o){Ll(t,e[n],e[s],e[i],e.scale,r,o)}const Nl=["x","scaleX","originX"],El=["y","scaleY","originY"];function Qn(t,e,n,s){Zn(t.x,e,Nl,n==null?void 0:n.x,s==null?void 0:s.x),Zn(t.y,e,El,n==null?void 0:n.y,s==null?void 0:s.y)}function Jn(t){return t.translate===0&&t.scale===1}function Vi(t){return Jn(t.x)&&Jn(t.y)}function Ti(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function ts(t){return E(t.x)/E(t.y)}class Fl{constructor(){this.members=[]}add(e){We(this.members,e),e.scheduleRender()}remove(e){if(Xe(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){var s;const i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,n&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),!((s=e.root)===null||s===void 0)&&s.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{var n,s,i,r,o;(s=(n=e.options).onExitComplete)===null||s===void 0||s.call(n),(o=(i=e.resumingFrom)===null||i===void 0?void 0:(r=i.options).onExitComplete)===null||o===void 0||o.call(r)})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function es(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y;if((i||r)&&(s=`translate3d(${i}px, ${r}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(s+=`rotate(${l}deg) `),c&&(s+=`rotateX(${c}deg) `),u&&(s+=`rotateY(${u}deg) `)}const o=t.x.scale*e.x,a=t.y.scale*e.y;return(o!==1||a!==1)&&(s+=`scale(${o}, ${a})`),s||"none"}const Bl=(t,e)=>t.depth-e.depth;class kl{constructor(){this.children=[],this.isDirty=!1}add(e){We(this.children,e),this.isDirty=!0}remove(e){Xe(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Bl),this.isDirty=!1,this.children.forEach(e)}}const ns=["","X","Y","Z"],ss=1e3;let Ol=0;function Pi({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o,a={},l=e==null?void 0:e()){this.id=Ol++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(Ul),this.nodes.forEach(Gl),this.nodes.forEach(Wl)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=o,this.latestValues=a,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0,o&&this.root.registerPotentialNode(o,this);for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new kl)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new He),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l==null||l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}registerPotentialNode(o,a){this.potentialNodes.set(o,a)}mount(o,a=!1){var l;if(this.instance)return;this.isSVG=o instanceof SVGElement&&o.tagName!=="svg",this.instance=o;const{layoutId:c,layout:u,visualElement:f}=this.options;if(f&&!f.current&&f.mount(o),this.root.nodes.add(this),(l=this.parent)===null||l===void 0||l.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),a&&(u||c)&&(this.isLayoutDirty=!0),t){let d;const h=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=si(h,250),pt.hasAnimatedSinceResize&&(pt.hasAnimatedSinceResize=!1,this.nodes.forEach(os))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&f&&(c||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:p,layout:g})=>{var w,v,V,x,b;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const P=(v=(w=this.options.transition)!==null&&w!==void 0?w:f.getDefaultTransition())!==null&&v!==void 0?v:ql,{onLayoutAnimationStart:S,onLayoutAnimationComplete:L}=f.getProps(),O=!this.targetLayout||!Ti(this.targetLayout,g)||p,F=!h&&p;if(!((V=this.resumeFrom)===null||V===void 0)&&V.instance||F||h&&(O||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,F);const D={...ii(P,"layout"),onPlay:S,onComplete:L};f.shouldReduceMotion&&(D.delay=0,D.type=!1),this.startAnimation(D)}else!h&&this.animationProgress===0&&os(this),this.isLead()&&((b=(x=this.options).onExitComplete)===null||b===void 0||b.call(x));this.targetLayout=g})}unmount(){var o,a;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(o=this.getStack())===null||o===void 0||o.remove(this),(a=this.parent)===null||a===void 0||a.children.delete(this),this.instance=void 0,Y.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var o;return this.isAnimationBlocked||((o=this.parent)===null||o===void 0?void 0:o.isTreeAnimationBlocked())||!1}startUpdate(){var o;this.isUpdateBlocked()||(this.isUpdating=!0,(o=this.nodes)===null||o===void 0||o.forEach(Xl),this.animationId++)}willUpdate(o=!0){var a,l,c;if(this.root.isUpdateBlocked()){(l=(a=this.options).onExitComplete)===null||l===void 0||l.call(a);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let h=0;h<this.path.length;h++){const p=this.path[h];p.shouldResetTransform=!0,p.updateScroll("snapshot")}const{layoutId:u,layout:f}=this.options;if(u===void 0&&!f)return;const d=(c=this.options.visualElement)===null||c===void 0?void 0:c.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(is);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Zl),this.potentialNodes.clear()),this.nodes.forEach(zl),this.nodes.forEach(Il),this.nodes.forEach(_l),this.clearAllSnapshots(),ie.update(),ie.preRender(),ie.render())}clearAllSnapshots(){this.nodes.forEach($l),this.sharedNodes.forEach(Hl)}scheduleUpdateProjection(){N.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){N.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var o;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=M(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(o=this.options.visualElement)===null||o===void 0||o.notify("LayoutMeasure",this.layout.layoutBox,a==null?void 0:a.layoutBox)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){var o;if(!i)return;const a=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Vi(this.projectionDelta),c=(o=this.options.visualElement)===null||o===void 0?void 0:o.getProps().transformTemplate,u=c==null?void 0:c(this.latestValues,""),f=u!==this.prevTransformTemplateValue;a&&(l||J(this.latestValues)||f)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),Ql(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return M();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(H(a.x,l.offset.x),H(a.y,l.offset.y)),a}removeElementScroll(o){const a=M();k(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:f}=c;if(c!==this.root&&u&&f.layoutScroll){if(u.isRoot){k(a,o);const{scroll:d}=this.root;d&&(H(a.x,-d.offset.x),H(a.y,-d.offset.y))}H(a.x,u.offset.x),H(a.y,u.offset.y)}}return a}applyTransform(o,a=!1){const l=M();k(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&at(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),J(u.latestValues)&&at(l,u.latestValues)}return J(this.latestValues)&&at(l,this.latestValues),l}removeTransform(o){var a;const l=M();k(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!J(u.latestValues))continue;Ce(u.latestValues)&&u.updateSnapshot();const f=M(),d=u.measurePageBox();k(f,d),Qn(l,u.latestValues,(a=u.snapshot)===null||a===void 0?void 0:a.layoutBox,f)}return J(this.latestValues)&&Qn(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var o;const a=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:l,layoutId:c}=this.options;if(!(!this.layout||!(l||c))){if(!this.targetDelta&&!this.relativeTarget){const u=this.getClosestProjectingParent();u&&u.layout?(this.relativeParent=u,this.relativeTarget=M(),this.relativeTargetOrigin=M(),xt(this.relativeTargetOrigin,this.layout.layoutBox,u.layout.layoutBox),k(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=M(),this.targetWithTransforms=M()),this.relativeTarget&&this.relativeTargetOrigin&&(!((o=this.relativeParent)===null||o===void 0)&&o.target)?ka(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):k(this.target,this.layout.layoutBox),fi(this.target,this.targetDelta)):k(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const u=this.getClosestProjectingParent();u&&!!u.resumingFrom==!!this.resumingFrom&&!u.options.layoutScroll&&u.target?(this.relativeParent=u,this.relativeTarget=M(),this.relativeTargetOrigin=M(),xt(this.relativeTargetOrigin,this.target,u.target),k(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ce(this.parent.latestValues)||ui(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var o;const{isProjectionDirty:a,isTransformDirty:l}=this;this.isProjectionDirty=this.isTransformDirty=!1;const c=this.getLead(),u=!!this.resumingFrom||this!==c;let f=!0;if(a&&(f=!1),u&&l&&(f=!1),f)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(!((o=this.parent)===null||o===void 0)&&o.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;k(this.layoutCorrected,this.layout.layoutBox),Xa(this.layoutCorrected,this.treeScale,this.path,u);const{target:p}=c;if(!p)return;this.projectionDelta||(this.projectionDelta=bt(),this.projectionDeltaWithTransform=bt());const g=this.treeScale.x,w=this.treeScale.y,v=this.projectionTransform;vt(this.projectionDelta,this.layoutCorrected,p,this.latestValues),this.projectionTransform=es(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==g||this.treeScale.y!==w)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a,l,c;(l=(a=this.options).scheduleRender)===null||l===void 0||l.call(a),o&&((c=this.getStack())===null||c===void 0||c.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){var l,c;const u=this.snapshot,f=(u==null?void 0:u.latestValues)||{},d={...this.latestValues},h=bt();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!a;const p=M(),g=(u==null?void 0:u.source)!==((l=this.layout)===null||l===void 0?void 0:l.source),w=(((c=this.getStack())===null||c===void 0?void 0:c.members.length)||0)<=1,v=!!(g&&!w&&this.options.crossfade===!0&&!this.path.some(Yl));this.animationProgress=0,this.mixTargetDelta=V=>{var x;const b=V/1e3;rs(h.x,o.x,b),rs(h.y,o.y,b),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((x=this.relativeParent)===null||x===void 0)&&x.layout)&&(xt(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Kl(this.relativeTarget,this.relativeTargetOrigin,p,b)),g&&(this.animationValues=d,jl(d,f,this.latestValues,b,v,w)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=b},this.mixTargetDelta(0)}startAnimation(o){var a,l;this.notifyListeners("animationStart"),(a=this.currentAnimation)===null||a===void 0||a.stop(),this.resumingFrom&&((l=this.resumingFrom.currentAnimation)===null||l===void 0||l.stop()),this.pendingAnimation&&(Y.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=N.update(()=>{pt.hasAnimatedSinceResize=!0,this.currentAnimation=Cl(0,ss,{...o,onUpdate:c=>{var u;this.mixTargetDelta(c),(u=o.onUpdate)===null||u===void 0||u.call(o,c)},onComplete:()=>{var c;(c=o.onComplete)===null||c===void 0||c.call(o),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var o;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(o=this.getStack())===null||o===void 0||o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var o;this.currentAnimation&&((o=this.mixTargetDelta)===null||o===void 0||o.call(this,ss),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&Ai(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||M();const f=E(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+f;const d=E(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+d}k(a,l),at(a,u),vt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){var l,c,u;this.sharedNodes.has(o)||this.sharedNodes.set(o,new Fl),this.sharedNodes.get(o).add(a),a.promote({transition:(l=a.options.initialPromotionConfig)===null||l===void 0?void 0:l.transition,preserveFollowOpacity:(u=(c=a.options.initialPromotionConfig)===null||c===void 0?void 0:c.shouldPreserveFollowOpacity)===null||u===void 0?void 0:u.call(c,a)})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<ns.length;u++){const f="rotate"+ns[u];l[f]&&(c[f]=l[f],o.setStaticValue(f,0))}o==null||o.render();for(const u in c)o.setStaticValue(u,c[u]);o.scheduleRender()}getProjectionStyles(o={}){var a,l,c;const u={};if(!this.instance||this.isSVG)return u;if(this.isVisible)u.visibility="";else return{visibility:"hidden"};const f=(a=this.options.visualElement)===null||a===void 0?void 0:a.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Nt(o.pointerEvents)||"",u.transform=f?f(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=Nt(o.pointerEvents)||""),this.hasProjected&&!J(this.latestValues)&&(w.transform=f?f({},""):"none",this.hasProjected=!1),w}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=es(this.projectionDeltaWithTransform,this.treeScale,h),f&&(u.transform=f(h,u.transform));const{x:p,y:g}=this.projectionDelta;u.transformOrigin=`${p.origin*100}% ${g.origin*100}% 0`,d.animationValues?u.opacity=d===this?(c=(l=h.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const w in Ft){if(h[w]===void 0)continue;const{correct:v,applyTo:V}=Ft[w],x=v(h[w],d);if(V){const b=V.length;for(let P=0;P<b;P++)u[V[P]]=x}else u[w]=x}return this.options.layoutId&&(u.pointerEvents=d===this?Nt(o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(is),this.root.sharedNodes.clear()}}}function Il(t){t.updateLayout()}function _l(t){var e,n,s;const i=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=t.layout,{animationType:a}=t.options,l=i.source!==t.layout.source;a==="size"?I(h=>{const p=l?i.measuredBox[h]:i.layoutBox[h],g=E(p);p.min=r[h].min,p.max=p.min+g}):Ai(a,i.layoutBox,r)&&I(h=>{const p=l?i.measuredBox[h]:i.layoutBox[h],g=E(r[h]);p.max=p.min+g});const c=bt();vt(c,r,i.layoutBox);const u=bt();l?vt(u,t.applyTransform(o,!0),i.measuredBox):vt(u,r,i.layoutBox);const f=!Vi(c);let d=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:p,layout:g}=h;if(p&&g){const w=M();xt(w,i.layoutBox,p.layoutBox);const v=M();xt(v,r,g.layoutBox),Ti(w,v)||(d=!0)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:i,delta:u,layoutDelta:c,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else t.isLead()&&((s=(n=t.options).onExitComplete)===null||s===void 0||s.call(n));t.options.transition=void 0}function Ul(t){t.isProjectionDirty||(t.isProjectionDirty=!!(t.parent&&t.parent.isProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=!!(t.parent&&t.parent.isTransformDirty))}function $l(t){t.clearSnapshot()}function is(t){t.clearMeasurements()}function zl(t){const{visualElement:e}=t.options;e!=null&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function os(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0}function Gl(t){t.resolveTargetDelta()}function Wl(t){t.calcProjection()}function Xl(t){t.resetRotation()}function Hl(t){t.removeLeadSnapshot()}function rs(t,e,n){t.translate=C(e.translate,0,n),t.scale=C(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function as(t,e,n,s){t.min=C(e.min,n.min,s),t.max=C(e.max,n.max,s)}function Kl(t,e,n,s){as(t.x,e.x,n.x,s),as(t.y,e.y,n.y,s)}function Yl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const ql={duration:.45,ease:[.4,0,.1,1]};function Zl(t,e){let n=t.root;for(let r=t.path.length-1;r>=0;r--)if(t.path[r].instance){n=t.path[r];break}const i=(n&&n!==t.root?n.instance:document).querySelector(`[data-projection-id="${e}"]`);i&&t.mount(i,!0)}function ls(t){t.min=Math.round(t.min),t.max=Math.round(t.max)}function Ql(t){ls(t.x),ls(t.y)}function Ai(t,e,n){return t==="position"||t==="preserve-aspect"&&!Ae(ts(e),ts(n),.2)}const Jl=Pi({attachResizeListener:(t,e)=>Zt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),me={current:void 0},tc=Pi({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!me.current){const t=new Jl(0,{});t.mount(window),t.setOptions({layoutScroll:!0}),me.current=t}return me.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),ec={...Na,...Zo,...el,...Sl},nc=qi((t,e)=>jo(t,e,ec,wl,tc));function sc({title:t,titleId:e,...n},s){return y.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":e},n),t?y.createElement("title",{id:e},t):null,y.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const ic=y.forwardRef(sc);function oc(){const[t,e]=y.useState(!1),[n,s]=y.useState(!1),i=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}];return y.useEffect(()=>{const r=()=>{s(window.scrollY>50)};return window.addEventListener("scroll",r),()=>window.removeEventListener("scroll",r)},[]),m.jsx(nc.header,{className:`bg-white sticky top-0 z-50 transition-all duration-300 ${n?"shadow-lg py-2":"shadow-sm py-0"}`,initial:{y:-100},animate:{y:0},transition:{duration:.3},children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:`flex justify-between items-center transition-all duration-300 ${n?"h-14":"h-16"}`,children:[m.jsx("div",{className:"flex-shrink-0",children:m.jsxs(_,{href:"/",className:"flex items-center",children:[m.jsx("img",{src:"/images/logo.png",alt:"BelgicaLaw",className:`w-auto transition-all duration-300 ${n?"h-6":"h-8"}`}),m.jsx("span",{className:"ml-2 text-3xl font-serif font-bold text-secondary-900",children:"BelgicaLaw"})]})}),m.jsx("nav",{className:"hidden md:flex space-x-8",children:i.map(r=>m.jsx(_,{href:r.href,className:"text-secondary-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200",children:r.name},r.name))}),m.jsx("div",{className:"hidden md:flex",children:m.jsx(_,{href:"/contact",className:"btn-primary",children:"Get Consultation"})}),m.jsx("div",{className:"md:hidden",children:m.jsx("button",{onClick:()=>e(!t),className:"text-secondary-600 hover:text-secondary-900 p-2",children:t?m.jsx(Ne,{className:"h-6 w-6"}):m.jsx(ji,{className:"h-6 w-6"})})})]}),t&&m.jsx("div",{className:"md:hidden",children:m.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-secondary-200",children:[i.map(r=>m.jsx(_,{href:r.href,className:"text-secondary-600 hover:text-primary-600 block px-3 py-2 text-base font-medium",onClick:()=>e(!1),children:r.name},r.name)),m.jsx(_,{href:"/contact",className:"btn-primary block text-center mt-4",onClick:()=>e(!1),children:"Get Consultation"})]})})]})})}function rc(){return m.jsx("footer",{className:"bg-secondary-900 text-white",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[m.jsxs("div",{className:"col-span-1 md:col-span-2",children:[m.jsxs("div",{className:"flex items-center mb-4",children:[m.jsx("img",{src:"/images/logo.png",alt:"BelgicaLaw",className:"h-8 w-auto filter brightness-0 invert"}),m.jsx("span",{className:"ml-2 text-xl font-serif font-bold",children:"BelgicaLaw"})]}),m.jsx("p",{className:"text-secondary-300 mb-4 max-w-md",children:"Professional legal consultation and notarial services in Metro Manila. Committed to providing reliable and trustworthy legal assistance."}),m.jsxs("div",{className:"flex space-x-4",children:[m.jsx(_,{href:"/about",className:"text-secondary-300 hover:text-white transition-colors",children:"About"}),m.jsx(_,{href:"/services",className:"text-secondary-300 hover:text-white transition-colors",children:"Services"}),m.jsx(_,{href:"/contact",className:"text-secondary-300 hover:text-white transition-colors",children:"Contact"})]})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Contact Info"}),m.jsxs("div",{className:"space-y-3",children:[m.jsxs("div",{className:"flex items-start",children:[m.jsx(Li,{className:"h-5 w-5 text-primary-400 mt-0.5 mr-3 flex-shrink-0"}),m.jsx("span",{className:"text-secondary-300 text-sm",children:"Metro Manila, Philippines"})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx(ic,{className:"h-5 w-5 text-primary-400 mr-3 flex-shrink-0"}),m.jsx("span",{className:"text-secondary-300 text-sm",children:"+63 XXX XXX XXXX"})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx(Di,{className:"h-5 w-5 text-primary-400 mr-3 flex-shrink-0"}),m.jsx("span",{className:"text-secondary-300 text-sm",children:"<EMAIL>"})]})]})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Office Hours"}),m.jsx("div",{className:"space-y-2",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(Ni,{className:"h-5 w-5 text-primary-400 mr-3 flex-shrink-0"}),m.jsxs("div",{className:"text-secondary-300 text-sm",children:[m.jsx("div",{children:"Mon - Fri: 9:00 AM - 6:00 PM"}),m.jsx("div",{children:"Sat: 9:00 AM - 12:00 PM"}),m.jsx("div",{children:"Sun: Closed"})]})]})})]})]}),m.jsx("div",{className:"border-t border-secondary-800 mt-8 pt-8",children:m.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[m.jsxs("p",{className:"text-secondary-400 text-sm",children:["© ",new Date().getFullYear()," BelgicaLaw. All rights reserved."]}),m.jsxs("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[m.jsx(_,{href:"/privacy",className:"text-secondary-400 hover:text-white text-sm transition-colors",children:"Privacy Policy"}),m.jsx(_,{href:"/terms",className:"text-secondary-400 hover:text-white text-sm transition-colors",children:"Terms of Service"})]})]})})]})})}function ac(){const[t,e]=y.useState(!1),{data:n,setData:s,post:i,processing:r,errors:o,reset:a}=Si({name:"",email:"",message:""}),l=c=>{c.preventDefault(),i("/inquiry",{onSuccess:()=>{a(),e(!1)}})};return m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"fixed bottom-6 right-6 z-50",children:m.jsx("button",{onClick:()=>e(!t),className:"bg-primary-600 hover:bg-primary-700 text-white p-4 rounded-full shadow-lg transition-colors duration-200",children:t?m.jsx(Ne,{className:"h-6 w-6"}):m.jsx(Ri,{className:"h-6 w-6"})})}),t&&m.jsxs("div",{className:"fixed bottom-24 right-6 w-80 bg-white rounded-lg shadow-xl border z-50",children:[m.jsxs("div",{className:"bg-primary-600 text-white p-4 rounded-t-lg",children:[m.jsx("h3",{className:"font-semibold",children:"Quick Inquiry"}),m.jsx("p",{className:"text-sm text-primary-100",children:"Send us a message"})]}),m.jsxs("form",{onSubmit:l,className:"p-4 space-y-4",children:[m.jsxs("div",{children:[m.jsx("input",{type:"text",placeholder:"Your Name (Optional)",value:n.name,onChange:c=>s("name",c.target.value),className:"w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),o.name&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:o.name})]}),m.jsxs("div",{children:[m.jsx("input",{type:"email",placeholder:"Your Email (Optional)",value:n.email,onChange:c=>s("email",c.target.value),className:"w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),o.email&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:o.email})]}),m.jsxs("div",{children:[m.jsx("textarea",{placeholder:"Your message...",rows:4,value:n.message,onChange:c=>s("message",c.target.value),className:"w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none",required:!0}),o.message&&m.jsx("p",{className:"text-red-500 text-xs mt-1",children:o.message})]}),m.jsx("button",{type:"submit",disabled:r,className:"w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center",children:r?m.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):m.jsxs(m.Fragment,{children:["Send Message",m.jsx(Ei,{className:"ml-2 h-4 w-4"})]})})]}),m.jsx("div",{className:"px-4 pb-4",children:m.jsx("p",{className:"text-xs text-secondary-500 text-center",children:"We'll get back to you as soon as possible"})})]})]})}function lc(){const[t,e]=y.useState(!1),n="+63XXXXXXXXXX",s="Hello! I would like to inquire about your legal services.",i=()=>{const r=`https://wa.me/${n.replace(/[^0-9]/g,"")}?text=${encodeURIComponent(s)}`;window.open(r,"_blank")};return m.jsx("div",{className:"fixed bottom-6 left-6 z-50",children:m.jsxs("div",{className:"relative",children:[t&&m.jsxs("div",{className:"absolute bottom-full left-0 mb-2 px-3 py-2 bg-secondary-800 text-white text-sm rounded-lg whitespace-nowrap",children:["Chat with us on WhatsApp",m.jsx("div",{className:"absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-secondary-800"})]}),m.jsx("button",{onClick:i,onMouseEnter:()=>e(!0),onMouseLeave:()=>e(!1),className:"bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg transition-colors duration-200 flex items-center justify-center",children:m.jsx("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:m.jsx("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"})})})]})})}function cc(){const{flash:t}=Ci().props,[e,n]=y.useState(!1),[s,i]=y.useState(""),[r,o]=y.useState("");return y.useEffect(()=>{t!=null&&t.success?(i(t.success),o("success"),n(!0)):t!=null&&t.error&&(i(t.error),o("error"),n(!0))},[t]),y.useEffect(()=>{if(e){const a=setTimeout(()=>{n(!1)},5e3);return()=>clearTimeout(a)}},[e]),e?m.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-sm",children:m.jsx("div",{className:`rounded-lg p-4 shadow-lg ${r==="success"?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:m.jsxs("div",{className:"flex items-start",children:[m.jsx("div",{className:"flex-shrink-0",children:r==="success"?m.jsx(Fi,{className:"h-5 w-5 text-green-400"}):m.jsx(Bi,{className:"h-5 w-5 text-red-400"})}),m.jsx("div",{className:"ml-3 flex-1",children:m.jsx("p",{className:`text-sm font-medium ${r==="success"?"text-green-800":"text-red-800"}`,children:s})}),m.jsx("div",{className:"ml-4 flex-shrink-0",children:m.jsx("button",{onClick:()=>n(!1),className:`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${r==="success"?"text-green-500 hover:bg-green-100 focus:ring-green-600":"text-red-500 hover:bg-red-100 focus:ring-red-600"}`,children:m.jsx(Ne,{className:"h-4 w-4"})})})]})})}):null}function vc({children:t,title:e="BelgicaLaw"}){return m.jsxs("div",{className:"min-h-screen bg-gray-50",children:[m.jsx(Mi,{title:e}),m.jsx(oc,{}),m.jsx("main",{className:"flex-1",children:t}),m.jsx(rc,{}),m.jsx(ac,{}),m.jsx(lc,{}),m.jsx(cc,{})]})}export{ic as F,vc as L,Kt as P,Fe as a,fs as b,Ls as c,nc as m,N as s,ne as u};
