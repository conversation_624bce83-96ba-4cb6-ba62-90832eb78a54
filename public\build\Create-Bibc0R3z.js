import{x as b,j as e,Q as p,t as n}from"./app2.js";import{A as v}from"./AdminLayout-CA9t8iTo.js";import{F as j}from"./ArrowLeftIcon-3sOzk9Cr.js";import{F as y}from"./TrashIcon-CkX595jR.js";import{F as N}from"./PlusIcon-CFTIzR_P.js";import"./HomeIcon-BtCkMBaV.js";import"./XMarkIcon-DvlfLOeu.js";import"./EyeIcon-CuARlRRc.js";function B({auth:d}){const{data:r,setData:a,post:c,processing:l,errors:s}=b({title:"",description:"",features:[""],icon:"ScaleIcon",image:null,sort_order:0,is_active:!0}),m=[{value:"ScaleIcon",label:"⚖️ Scale (Legal)"},{value:"DocumentTextIcon",label:"📄 Document"},{value:"UserGroupIcon",label:"👥 User Group"},{value:"ShieldCheckIcon",label:"🛡️ Shield (Protection)"},{value:"HomeIcon",label:"🏠 Home (Real Estate)"},{value:"HeartIcon",label:"❤️ Heart (Family)"},{value:"BriefcaseIcon",label:"💼 Briefcase (Business)"},{value:"BuildingOfficeIcon",label:"🏢 Building (Corporate)"}],x=()=>{a("features",[...r.features,""])},u=t=>{const i=r.features.filter((o,h)=>h!==t);a("features",i)},g=(t,i)=>{const o=[...r.features];o[t]=i,a("features",o)},f=t=>{t.preventDefault(),c("/admin/services",{forceFormData:!0})};return e.jsxs(v,{user:d.user,children:[e.jsx(p,{title:"Create Service"}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(n,{href:"/admin/services",className:"mr-4 text-gray-500 hover:text-gray-700",children:e.jsx(j,{className:"h-5 w-5"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Service"})]}),e.jsx("p",{className:"text-gray-600",children:"Add a new legal service to your website"})]}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:e.jsxs("form",{onSubmit:f,className:"space-y-6 p-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700",children:"Service Title *"}),e.jsx("input",{type:"text",id:"title",value:r.title,onChange:t=>a("title",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"e.g., Legal Consultation"}),s.title&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.title})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description *"}),e.jsx("textarea",{id:"description",rows:4,value:r.description,onChange:t=>a("description",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Describe this service..."}),s.description&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.description})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700",children:"Icon *"}),e.jsx("select",{id:"icon",value:r.icon,onChange:t=>a("icon",t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:m.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))}),s.icon&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.icon})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700",children:"Service Image"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:t=>a("image",t.target.files[0]),className:"mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"}),s.image&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.image}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Upload an image to showcase this service. Recommended size: 800x600px. Max size: 2MB."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Features *"}),r.features.map((t,i)=>e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("input",{type:"text",value:t,onChange:o=>g(i,o.target.value),className:"flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Enter a feature..."}),r.features.length>1&&e.jsx("button",{type:"button",onClick:()=>u(i),className:"text-red-600 hover:text-red-800",children:e.jsx(y,{className:"h-5 w-5"})})]},i)),e.jsxs("button",{type:"button",onClick:x,className:"mt-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(N,{className:"h-4 w-4 mr-1"}),"Add Feature"]}),s.features&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.features})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"sort_order",className:"block text-sm font-medium text-gray-700",children:"Sort Order *"}),e.jsx("input",{type:"number",id:"sort_order",min:"0",value:r.sort_order,onChange:t=>a("sort_order",parseInt(t.target.value)),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"}),s.sort_order&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:s.sort_order})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"is_active",type:"checkbox",checked:r.is_active,onChange:t=>a("is_active",t.target.checked),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Active (visible on website)"})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[e.jsx(n,{href:"/admin/services",className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:l,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:l?"Creating...":"Create Service"})]})]})})]})})]})}export{B as default};
