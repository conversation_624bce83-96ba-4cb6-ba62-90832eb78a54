# Email Configuration for Belgica Law Office
# Copy these settings to your .env file and update with your actual SMTP credentials

# Mail Driver - Use 'smtp' for production
MAIL_MAILER=smtp

# SMTP Server Settings
# For Gmail:
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# For Outlook/Hotmail:
# MAIL_HOST=smtp-mail.outlook.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-password
# MAIL_ENCRYPTION=tls

# For Yahoo:
# MAIL_HOST=smtp.mail.yahoo.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_ENCRYPTION=tls

# For Custom SMTP (like cPanel hosting):
# MAIL_HOST=mail.yourdomain.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-password
# MAIL_ENCRYPTION=tls

# From Address (appears as sender)
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Belgica Law Office"

# Admin Email (where contact forms are sent)
MAIL_ADMIN_EMAIL=<EMAIL>

# Additional Settings
MAIL_EHLO_DOMAIN=belgicalaw.com

# For testing purposes, you can use 'log' driver to save emails to storage/logs/laravel.log
# MAIL_MAILER=log

# Instructions:
# 1. Choose your email provider settings above
# 2. Update MAIL_USERNAME and MAIL_PASSWORD with your credentials
# 3. For Gmail, use App Passwords instead of your regular password
# 4. Update MAIL_FROM_ADDRESS and MAIL_ADMIN_EMAIL with your actual email addresses
# 5. Test the configuration using the test command: php artisan tinker
#    Then run: Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
