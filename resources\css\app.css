@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    body {
        font-family: 'Inter', sans-serif;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: 'Merriweather', serif;
    }
}

@layer components {
    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }

    .btn-secondary {
        @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }

    .card {
        @apply bg-white rounded-lg shadow-md p-6;
    }

    .hero-gradient {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    }
}
