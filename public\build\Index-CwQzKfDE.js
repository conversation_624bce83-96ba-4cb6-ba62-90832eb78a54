import{x as k,j as e,Q as w}from"./app2.js";import{A as C,F as n}from"./AdminLayout-CA9t8iTo.js";import{F as S}from"./MapPinIcon-HgHdBN5q.js";import{F}from"./HomeIcon-BtCkMBaV.js";import{F as $}from"./XMarkIcon-DvlfLOeu.js";import"./EyeIcon-CuARlRRc.js";function D({auth:p,settings:c,flash:o}){const d={};Object.values(c).flat().forEach(r=>{d[r.key]=r.value||""});const{data:a,setData:g,post:f,processing:m,errors:x}=k({settings:d}),y=r=>{r.preventDefault(),f("/admin/settings")},i=(r,l)=>{g("settings",{...a.settings,[r]:l})},b=r=>({general:n,contact:$,content:F})[r]||n,j=r=>({general:"text-blue-600",contact:"text-green-600",content:"text-purple-600"})[r]||"text-gray-600";return e.jsxs(C,{user:p.user,children:[e.jsx(w,{title:"Settings"}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Website Settings"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Configure your website's general settings and information"})]}),(o==null?void 0:o.success)&&e.jsx("div",{className:"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:o.success}),e.jsxs("form",{onSubmit:y,className:"space-y-8",children:[Object.entries(c).map(([r,l])=>{const v=b(r),N=j(r);return e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{className:`h-6 w-6 ${N} mr-3`}),e.jsxs("h3",{className:"text-lg font-medium text-gray-900 capitalize",children:[r," Settings"]})]})}),e.jsx("div",{className:"px-6 py-6 space-y-6",children:l.map(s=>{var u,h;return e.jsxs("div",{children:[e.jsx("label",{htmlFor:s.key,className:"block text-sm font-medium text-gray-700",children:s.description||s.key.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase())}),s.type==="textarea"?e.jsx("textarea",{id:s.key,rows:4,value:a.settings[s.key]||"",onChange:t=>i(s.key,t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:`Enter ${((u=s.description)==null?void 0:u.toLowerCase())||s.key}`}):s.type==="boolean"?e.jsx("div",{className:"mt-1",children:e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.settings[s.key]==="1"||a.settings[s.key]===!0,onChange:t=>i(s.key,t.target.checked?"1":"0"),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Enable this setting"})]})}):e.jsx("input",{type:s.type==="email"?"email":"text",id:s.key,value:a.settings[s.key]||"",onChange:t=>i(s.key,t.target.value),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:s.key==="office_latitude"?"e.g., 14.5995":s.key==="office_longitude"?"e.g., 120.9842":s.key==="map_zoom_level"?"e.g., 15 (1-20)":`Enter ${((h=s.description)==null?void 0:h.toLowerCase())||s.key}`,step:s.key.includes("latitude")||s.key.includes("longitude")?"0.000001":void 0}),x[`settings.${s.key}`]&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x[`settings.${s.key}`]}),s.description&&e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:s.description})]},s.key)})})]},r)}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",disabled:m,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:m?"Saving...":"Save Settings"})})]}),e.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(n,{className:"h-5 w-5 text-blue-400"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"About Settings"}),e.jsx("div",{className:"mt-2 text-sm text-blue-700",children:e.jsx("p",{children:"These settings control various aspects of your website. Changes will be reflected immediately on your live site. Make sure to test your changes after saving."})})]})]})}),e.jsx("div",{className:"mt-4 bg-green-50 border border-green-200 rounded-lg p-6",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(S,{className:"h-5 w-5 text-green-400"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-green-800",children:"Map Settings Help"}),e.jsxs("div",{className:"mt-2 text-sm text-green-700",children:[e.jsx("p",{className:"mb-2",children:"To find your office coordinates:"}),e.jsxs("ol",{className:"list-decimal list-inside space-y-1",children:[e.jsxs("li",{children:["Go to ",e.jsx("a",{href:"https://www.google.com/maps",target:"_blank",rel:"noopener noreferrer",className:"underline",children:"Google Maps"})]}),e.jsx("li",{children:"Search for your office address"}),e.jsx("li",{children:"Right-click on the exact location"}),e.jsx("li",{children:'Click on the coordinates that appear (e.g., "14.5995, 120.9842")'}),e.jsx("li",{children:"Copy the latitude (first number) and longitude (second number)"})]}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Zoom Level:"})," Use 1-20 (1 = world view, 20 = building level). Recommended: 15-17 for office locations."]})]})]})]})})]})})]})}export{D as default};
