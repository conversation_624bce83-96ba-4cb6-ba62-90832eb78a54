import{r as I,R as F,j as b,t as pe,a as Ct,g as Pt}from"./app2.js";import{u as et,s as Mt,a as It,P as Ot,b as Lt,c as jt,m as q,L as Nt}from"./Layout-BNjch6WH.js";import{F as At,a as zt,b as Vt,c as tt,d as it,e as Rt}from"./UserGroupIcon-CoGKUL_Y.js";import{a as _t,F as st}from"./HomeIcon-BtCkMBaV.js";import{F as be}from"./ScaleIcon-CNgvpsgc.js";import{b as $e}from"./ArticleCard-CNVorRTo.js";import{B as Dt}from"./BlogSection-Ckkp2kOi.js";import{F as Ft}from"./CheckCircleIcon-C2jnGy7x.js";import"./XMarkIcon-DvlfLOeu.js";import"./MapPinIcon-HgHdBN5q.js";import"./ClockIcon-D-sImXRf.js";import"./PaperAirplaneIcon-5vIWJYTj.js";import"./XCircleIcon-Bp7te63t.js";import"./TagIcon-CbLiXAwx.js";import"./CalendarIcon-C9X2ldo6.js";import"./EyeIcon-CuARlRRc.js";import"./SparklesIcon-C6eAv8sl.js";const He=new Set;function Bt(t,e,i){He.has(e)||(console.warn(e),He.add(e))}function rt(){const t=I.useRef(!1);return et(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function $t(){const t=rt(),[e,i]=I.useState(0),s=I.useCallback(()=>{t.current&&i(e+1)},[e]);return[I.useCallback(()=>Mt.postRender(s),[s]),e]}class Gt extends I.Component{getSnapshotBeforeUpdate(e){const i=this.props.childRef.current;if(i&&e.isPresent&&!this.props.isPresent){const s=this.props.sizeRef.current;s.height=i.offsetHeight||0,s.width=i.offsetWidth||0,s.top=i.offsetTop,s.left=i.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function kt({children:t,isPresent:e}){const i=I.useId(),s=I.useRef(null),n=I.useRef({width:0,height:0,top:0,left:0});return I.useInsertionEffect(()=>{const{width:r,height:a,top:l,left:d}=n.current;if(e||!s.current||!r||!a)return;s.current.dataset.motionPopId=i;const o=document.createElement("style");return document.head.appendChild(o),o.sheet&&o.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${r}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${d}px !important;
          }
        `),()=>{document.head.removeChild(o)}},[e]),I.createElement(Gt,{isPresent:e,childRef:s,sizeRef:n},I.cloneElement(t,{ref:s}))}const Oe=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:n,presenceAffectsLayout:r,mode:a})=>{const l=It(Ht),d=I.useId(),o=I.useMemo(()=>({id:d,initial:e,isPresent:i,custom:n,onExitComplete:c=>{l.set(c,!0);for(const u of l.values())if(!u)return;s&&s()},register:c=>(l.set(c,!1),()=>l.delete(c))}),r?void 0:[i]);return I.useMemo(()=>{l.forEach((c,u)=>l.set(u,!1))},[i]),I.useEffect(()=>{!i&&!l.size&&s&&s()},[i]),a==="popLayout"&&(t=I.createElement(kt,{isPresent:i},t)),I.createElement(Ot.Provider,{value:o},t)};function Ht(){return new Map}const ne=t=>t.key||"";function Wt(t,e){t.forEach(i=>{const s=ne(i);e.set(s,i)})}function qt(t){const e=[];return I.Children.forEach(t,i=>{I.isValidElement(i)&&e.push(i)}),e}const Ut=({children:t,custom:e,initial:i=!0,onExitComplete:s,exitBeforeEnter:n,presenceAffectsLayout:r=!0,mode:a="sync"})=>{n&&(a="wait",Bt(!1,"Replace exitBeforeEnter with mode='wait'"));let[l]=$t();const d=I.useContext(Lt).forceRender;d&&(l=d);const o=rt(),c=qt(t);let u=c;const g=new Set,x=I.useRef(u),m=I.useRef(new Map).current,T=I.useRef(!0);if(et(()=>{T.current=!1,Wt(c,m),x.current=u}),jt(()=>{T.current=!0,m.clear(),g.clear()}),T.current)return I.createElement(I.Fragment,null,u.map(p=>I.createElement(Oe,{key:ne(p),isPresent:!0,initial:i?void 0:!1,presenceAffectsLayout:r,mode:a},p)));u=[...u];const P=x.current.map(ne),v=c.map(ne),f=P.length;for(let p=0;p<f;p++){const S=P[p];v.indexOf(S)===-1&&g.add(S)}return a==="wait"&&g.size&&(u=[]),g.forEach(p=>{if(v.indexOf(p)!==-1)return;const S=m.get(p);if(!S)return;const C=P.indexOf(p),L=()=>{m.delete(p),g.delete(p);const A=x.current.findIndex(y=>y.key===p);if(x.current.splice(A,1),!g.size){if(x.current=c,o.current===!1)return;l(),s&&s()}};u.splice(C,0,I.createElement(Oe,{key:ne(S),isPresent:!1,onExitComplete:L,custom:e,presenceAffectsLayout:r,mode:a},S))}),u=u.map(p=>{const S=p.key;return g.has(S)?p:I.createElement(Oe,{key:ne(p),isPresent:!0,presenceAffectsLayout:r,mode:a},p)}),I.createElement(I.Fragment,null,g.size?u:u.map(p=>I.cloneElement(p)))};function Yt({title:t,titleId:e,...i},s){return I.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":e},i),t?I.createElement("title",{id:e},t):null,I.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}const Xt=I.forwardRef(Yt);function Kt({title:t,titleId:e,...i},s){return I.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":e},i),t?I.createElement("title",{id:e},t):null,I.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const Qt=I.forwardRef(Kt);var _e=new Map,ge=new WeakMap,We=0,Zt=void 0;function Jt(t){return t?(ge.has(t)||(We+=1,ge.set(t,We.toString())),ge.get(t)):"0"}function ei(t){return Object.keys(t).sort().filter(e=>t[e]!==void 0).map(e=>`${e}_${e==="root"?Jt(t.root):t[e]}`).toString()}function ti(t){const e=ei(t);let i=_e.get(e);if(!i){const s=new Map;let n;const r=new IntersectionObserver(a=>{a.forEach(l=>{var d;const o=l.isIntersecting&&n.some(c=>l.intersectionRatio>=c);t.trackVisibility&&typeof l.isVisible>"u"&&(l.isVisible=o),(d=s.get(l.target))==null||d.forEach(c=>{c(o,l)})})},t);n=r.thresholds||(Array.isArray(t.threshold)?t.threshold:[t.threshold||0]),i={id:e,observer:r,elements:s},_e.set(e,i)}return i}function ii(t,e,i={},s=Zt){if(typeof window.IntersectionObserver>"u"&&s!==void 0){const d=t.getBoundingClientRect();return e(s,{isIntersecting:s,target:t,intersectionRatio:typeof i.threshold=="number"?i.threshold:0,time:0,boundingClientRect:d,intersectionRect:d,rootBounds:d}),()=>{}}const{id:n,observer:r,elements:a}=ti(i),l=a.get(t)||[];return a.has(t)||a.set(t,l),l.push(e),r.observe(t),function(){l.splice(l.indexOf(e),1),l.length===0&&(a.delete(t),r.unobserve(t)),a.size===0&&(r.disconnect(),_e.delete(n))}}function si({threshold:t,delay:e,trackVisibility:i,rootMargin:s,root:n,triggerOnce:r,skip:a,initialInView:l,fallbackInView:d,onChange:o}={}){var c;const[u,g]=I.useState(null),x=I.useRef(o),[m,T]=I.useState({inView:!!l,entry:void 0});x.current=o,I.useEffect(()=>{if(a||!u)return;let p;return p=ii(u,(S,C)=>{T({inView:S,entry:C}),x.current&&x.current(S,C),C.isIntersecting&&r&&p&&(p(),p=void 0)},{root:n,rootMargin:s,threshold:t,trackVisibility:i,delay:e},d),()=>{p&&p()}},[Array.isArray(t)?t.toString():t,u,n,s,r,a,i,d,e]);const P=(c=m.entry)==null?void 0:c.target,v=I.useRef(void 0);!u&&P&&!r&&!a&&v.current!==P&&(v.current=P,T({inView:!!l,entry:void 0}));const f=[g,m.inView,m.entry];return f.ref=f[0],f.inView=f[1],f.entry=f[2],f}function qe(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function Ge(t,e){t===void 0&&(t={}),e===void 0&&(e={});const i=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>i.indexOf(s)<0).forEach(s=>{typeof t[s]>"u"?t[s]=e[s]:qe(e[s])&&qe(t[s])&&Object.keys(e[s]).length>0&&Ge(t[s],e[s])})}const nt={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function K(){const t=typeof document<"u"?document:{};return Ge(t,nt),t}const ri={document:nt,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function $(){const t=typeof window<"u"?window:{};return Ge(t,ri),t}function ni(t){return t===void 0&&(t=""),t.trim().split(" ").filter(e=>!!e.trim())}function ai(t){const e=t;Object.keys(e).forEach(i=>{try{e[i]=null}catch{}try{delete e[i]}catch{}})}function at(t,e){return e===void 0&&(e=0),setTimeout(t,e)}function Se(){return Date.now()}function li(t){const e=$();let i;return e.getComputedStyle&&(i=e.getComputedStyle(t,null)),!i&&t.currentStyle&&(i=t.currentStyle),i||(i=t.style),i}function oi(t,e){e===void 0&&(e="x");const i=$();let s,n,r;const a=li(t);return i.WebKitCSSMatrix?(n=a.transform||a.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(l=>l.replace(",",".")).join(", ")),r=new i.WebKitCSSMatrix(n==="none"?"":n)):(r=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),e==="x"&&(i.WebKitCSSMatrix?n=r.m41:s.length===16?n=parseFloat(s[12]):n=parseFloat(s[4])),e==="y"&&(i.WebKitCSSMatrix?n=r.m42:s.length===16?n=parseFloat(s[13]):n=parseFloat(s[5])),n||0}function ve(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function di(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function k(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const s=i<0||arguments.length<=i?void 0:arguments[i];if(s!=null&&!di(s)){const n=Object.keys(Object(s)).filter(r=>e.indexOf(r)<0);for(let r=0,a=n.length;r<a;r+=1){const l=n[r],d=Object.getOwnPropertyDescriptor(s,l);d!==void 0&&d.enumerable&&(ve(t[l])&&ve(s[l])?s[l].__swiper__?t[l]=s[l]:k(t[l],s[l]):!ve(t[l])&&ve(s[l])?(t[l]={},s[l].__swiper__?t[l]=s[l]:k(t[l],s[l])):t[l]=s[l])}}}return t}function we(t,e,i){t.style.setProperty(e,i)}function lt(t){let{swiper:e,targetPosition:i,side:s}=t;const n=$(),r=-e.translate;let a=null,l;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const o=i>r?"next":"prev",c=(g,x)=>o==="next"&&g>=x||o==="prev"&&g<=x,u=()=>{l=new Date().getTime(),a===null&&(a=l);const g=Math.max(Math.min((l-a)/d,1),0),x=.5-Math.cos(g*Math.PI)/2;let m=r+x*(i-r);if(c(m,i)&&(m=i),e.wrapperEl.scrollTo({[s]:m}),c(m,i)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:m})}),n.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=n.requestAnimationFrame(u)};u()}function ot(t){return t.querySelector(".swiper-slide-transform")||t.shadowRoot&&t.shadowRoot.querySelector(".swiper-slide-transform")||t}function X(t,e){e===void 0&&(e="");const i=$(),s=[...t.children];return i.HTMLSlotElement&&t instanceof HTMLSlotElement&&s.push(...t.assignedElements()),e?s.filter(n=>n.matches(e)):s}function ci(t,e){const i=[e];for(;i.length>0;){const s=i.shift();if(t===s)return!0;i.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function ui(t,e){const i=$();let s=e.contains(t);return!s&&i.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(t),s||(s=ci(t,e))),s}function Ee(t){try{console.warn(t);return}catch{}}function Te(t,e){e===void 0&&(e=[]);const i=document.createElement(t);return i.classList.add(...Array.isArray(e)?e:ni(e)),i}function fi(t,e){const i=[];for(;t.previousElementSibling;){const s=t.previousElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}function pi(t,e){const i=[];for(;t.nextElementSibling;){const s=t.nextElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}function te(t,e){return $().getComputedStyle(t,null).getPropertyValue(e)}function Ce(t){let e=t,i;if(e){for(i=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(i+=1);return i}}function dt(t,e){const i=[];let s=t.parentElement;for(;s;)e?s.matches(e)&&i.push(s):i.push(s),s=s.parentElement;return i}function mi(t,e){function i(s){s.target===t&&(e.call(t,s),t.removeEventListener("transitionend",i))}e&&t.addEventListener("transitionend",i)}function De(t,e,i){const s=$();return t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function B(t){return(Array.isArray(t)?t:[t]).filter(e=>!!e)}function Pe(t,e){e===void 0&&(e=""),typeof trustedTypes<"u"?t.innerHTML=trustedTypes.createPolicy("html",{createHTML:i=>i}).createHTML(e):t.innerHTML=e}let Le;function hi(){const t=$(),e=K();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function ct(){return Le||(Le=hi()),Le}let je;function gi(t){let{userAgent:e}=t===void 0?{}:t;const i=ct(),s=$(),n=s.navigator.platform,r=e||s.navigator.userAgent,a={ios:!1,android:!1},l=s.screen.width,d=s.screen.height,o=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const u=r.match(/(iPod)(.*OS\s([\d_]+))?/),g=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),x=n==="Win32";let m=n==="MacIntel";const T=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&m&&i.touch&&T.indexOf(`${l}x${d}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),m=!1),o&&!x&&(a.os="android",a.android=!0),(c||g||u)&&(a.os="ios",a.ios=!0),a}function ut(t){return t===void 0&&(t={}),je||(je=gi(t)),je}let Ne;function vi(){const t=$(),e=ut();let i=!1;function s(){const l=t.navigator.userAgent.toLowerCase();return l.indexOf("safari")>=0&&l.indexOf("chrome")<0&&l.indexOf("android")<0}if(s()){const l=String(t.navigator.userAgent);if(l.includes("Version/")){const[d,o]=l.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));i=d<16||d===16&&o<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),r=s(),a=r||n&&e.ios;return{isSafari:i||r,needPerspectiveFix:i,need3dFix:a,isWebView:n}}function ft(){return Ne||(Ne=vi()),Ne}function wi(t){let{swiper:e,on:i,emit:s}=t;const n=$();let r=null,a=null;const l=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(r=new ResizeObserver(u=>{a=n.requestAnimationFrame(()=>{const{width:g,height:x}=e;let m=g,T=x;u.forEach(P=>{let{contentBoxSize:v,contentRect:f,target:p}=P;p&&p!==e.el||(m=f?f.width:(v[0]||v).inlineSize,T=f?f.height:(v[0]||v).blockSize)}),(m!==g||T!==x)&&l()})}),r.observe(e.el))},o=()=>{a&&n.cancelAnimationFrame(a),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null)},c=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};i("init",()=>{if(e.params.resizeObserver&&typeof n.ResizeObserver<"u"){d();return}n.addEventListener("resize",l),n.addEventListener("orientationchange",c)}),i("destroy",()=>{o(),n.removeEventListener("resize",l),n.removeEventListener("orientationchange",c)})}function xi(t){let{swiper:e,extendParams:i,on:s,emit:n}=t;const r=[],a=$(),l=function(c,u){u===void 0&&(u={});const g=a.MutationObserver||a.WebkitMutationObserver,x=new g(m=>{if(e.__preventObserver__)return;if(m.length===1){n("observerUpdate",m[0]);return}const T=function(){n("observerUpdate",m[0])};a.requestAnimationFrame?a.requestAnimationFrame(T):a.setTimeout(T,0)});x.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:e.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),r.push(x)},d=()=>{if(e.params.observer){if(e.params.observeParents){const c=dt(e.hostEl);for(let u=0;u<c.length;u+=1)l(c[u])}l(e.hostEl,{childList:e.params.observeSlideChildren}),l(e.wrapperEl,{attributes:!1})}},o=()=>{r.forEach(c=>{c.disconnect()}),r.splice(0,r.length)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",d),s("destroy",o)}var yi={on(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const n=i?"unshift":"push";return t.split(" ").forEach(r=>{s.eventsListeners[r]||(s.eventsListeners[r]=[]),s.eventsListeners[r][n](e)}),s},once(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function n(){s.off(t,n),n.__emitterProxy&&delete n.__emitterProxy;for(var r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];e.apply(s,a)}return n.__emitterProxy=e,s.on(t,n,i)},onAny(t,e){const i=this;if(!i.eventsListeners||i.destroyed||typeof t!="function")return i;const s=e?"unshift":"push";return i.eventsAnyListeners.indexOf(t)<0&&i.eventsAnyListeners[s](t),i},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const i=e.eventsAnyListeners.indexOf(t);return i>=0&&e.eventsAnyListeners.splice(i,1),e},off(t,e){const i=this;return!i.eventsListeners||i.destroyed||!i.eventsListeners||t.split(" ").forEach(s=>{typeof e>"u"?i.eventsListeners[s]=[]:i.eventsListeners[s]&&i.eventsListeners[s].forEach((n,r)=>{(n===e||n.__emitterProxy&&n.__emitterProxy===e)&&i.eventsListeners[s].splice(r,1)})}),i},emit(){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsListeners)return t;let e,i,s;for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return typeof r[0]=="string"||Array.isArray(r[0])?(e=r[0],i=r.slice(1,r.length),s=t):(e=r[0].events,i=r[0].data,s=r[0].context||t),i.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(o=>{o.apply(s,[d,...i])}),t.eventsListeners&&t.eventsListeners[d]&&t.eventsListeners[d].forEach(o=>{o.apply(s,i)})}),t}};function bi(){const t=this;let e,i;const s=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=s.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?i=t.params.height:i=s.clientHeight,!(e===0&&t.isHorizontal()||i===0&&t.isVertical())&&(e=e-parseInt(te(s,"padding-left")||0,10)-parseInt(te(s,"padding-right")||0,10),i=i-parseInt(te(s,"padding-top")||0,10)-parseInt(te(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(i)&&(i=0),Object.assign(t,{width:e,height:i,size:t.isHorizontal()?e:i}))}function Si(){const t=this;function e(w,E){return parseFloat(w.getPropertyValue(t.getDirectionLabel(E))||0)}const i=t.params,{wrapperEl:s,slidesEl:n,size:r,rtlTranslate:a,wrongRTL:l}=t,d=t.virtual&&i.virtual.enabled,o=d?t.virtual.slides.length:t.slides.length,c=X(n,`.${t.params.slideClass}, swiper-slide`),u=d?t.virtual.slides.length:c.length;let g=[];const x=[],m=[];let T=i.slidesOffsetBefore;typeof T=="function"&&(T=i.slidesOffsetBefore.call(t));let P=i.slidesOffsetAfter;typeof P=="function"&&(P=i.slidesOffsetAfter.call(t));const v=t.snapGrid.length,f=t.slidesGrid.length;let p=i.spaceBetween,S=-T,C=0,L=0;if(typeof r>"u")return;typeof p=="string"&&p.indexOf("%")>=0?p=parseFloat(p.replace("%",""))/100*r:typeof p=="string"&&(p=parseFloat(p)),t.virtualSize=-p,c.forEach(w=>{a?w.style.marginLeft="":w.style.marginRight="",w.style.marginBottom="",w.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(we(s,"--swiper-centered-offset-before",""),we(s,"--swiper-centered-offset-after",""));const A=i.grid&&i.grid.rows>1&&t.grid;A?t.grid.initSlides(c):t.grid&&t.grid.unsetSlides();let y;const h=i.slidesPerView==="auto"&&i.breakpoints&&Object.keys(i.breakpoints).filter(w=>typeof i.breakpoints[w].slidesPerView<"u").length>0;for(let w=0;w<u;w+=1){y=0;let E;if(c[w]&&(E=c[w]),A&&t.grid.updateSlide(w,E,c),!(c[w]&&te(E,"display")==="none")){if(i.slidesPerView==="auto"){h&&(c[w].style[t.getDirectionLabel("width")]="");const M=getComputedStyle(E),O=E.style.transform,R=E.style.webkitTransform;if(O&&(E.style.transform="none"),R&&(E.style.webkitTransform="none"),i.roundLengths)y=t.isHorizontal()?De(E,"width"):De(E,"height");else{const V=e(M,"width"),j=e(M,"padding-left"),_=e(M,"padding-right"),N=e(M,"margin-left"),z=e(M,"margin-right"),D=M.getPropertyValue("box-sizing");if(D&&D==="border-box")y=V+N+z;else{const{clientWidth:G,offsetWidth:H}=E;y=V+j+_+N+z+(H-G)}}O&&(E.style.transform=O),R&&(E.style.webkitTransform=R),i.roundLengths&&(y=Math.floor(y))}else y=(r-(i.slidesPerView-1)*p)/i.slidesPerView,i.roundLengths&&(y=Math.floor(y)),c[w]&&(c[w].style[t.getDirectionLabel("width")]=`${y}px`);c[w]&&(c[w].swiperSlideSize=y),m.push(y),i.centeredSlides?(S=S+y/2+C/2+p,C===0&&w!==0&&(S=S-r/2-p),w===0&&(S=S-r/2-p),Math.abs(S)<1/1e3&&(S=0),i.roundLengths&&(S=Math.floor(S)),L%i.slidesPerGroup===0&&g.push(S),x.push(S)):(i.roundLengths&&(S=Math.floor(S)),(L-Math.min(t.params.slidesPerGroupSkip,L))%t.params.slidesPerGroup===0&&g.push(S),x.push(S),S=S+y+p),t.virtualSize+=y+p,C=y,L+=1}}if(t.virtualSize=Math.max(t.virtualSize,r)+P,a&&l&&(i.effect==="slide"||i.effect==="coverflow")&&(s.style.width=`${t.virtualSize+p}px`),i.setWrapperSize&&(s.style[t.getDirectionLabel("width")]=`${t.virtualSize+p}px`),A&&t.grid.updateWrapperSize(y,g),!i.centeredSlides){const w=[];for(let E=0;E<g.length;E+=1){let M=g[E];i.roundLengths&&(M=Math.floor(M)),g[E]<=t.virtualSize-r&&w.push(M)}g=w,Math.floor(t.virtualSize-r)-Math.floor(g[g.length-1])>1&&g.push(t.virtualSize-r)}if(d&&i.loop){const w=m[0]+p;if(i.slidesPerGroup>1){const E=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),M=w*i.slidesPerGroup;for(let O=0;O<E;O+=1)g.push(g[g.length-1]+M)}for(let E=0;E<t.virtual.slidesBefore+t.virtual.slidesAfter;E+=1)i.slidesPerGroup===1&&g.push(g[g.length-1]+w),x.push(x[x.length-1]+w),t.virtualSize+=w}if(g.length===0&&(g=[0]),p!==0){const w=t.isHorizontal()&&a?"marginLeft":t.getDirectionLabel("marginRight");c.filter((E,M)=>!i.cssMode||i.loop?!0:M!==c.length-1).forEach(E=>{E.style[w]=`${p}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let w=0;m.forEach(M=>{w+=M+(p||0)}),w-=p;const E=w>r?w-r:0;g=g.map(M=>M<=0?-T:M>E?E+P:M)}if(i.centerInsufficientSlides){let w=0;m.forEach(M=>{w+=M+(p||0)}),w-=p;const E=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(w+E<r){const M=(r-w-E)/2;g.forEach((O,R)=>{g[R]=O-M}),x.forEach((O,R)=>{x[R]=O+M})}}if(Object.assign(t,{slides:c,snapGrid:g,slidesGrid:x,slidesSizesGrid:m}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){we(s,"--swiper-centered-offset-before",`${-g[0]}px`),we(s,"--swiper-centered-offset-after",`${t.size/2-m[m.length-1]/2}px`);const w=-t.snapGrid[0],E=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(M=>M+w),t.slidesGrid=t.slidesGrid.map(M=>M+E)}if(u!==o&&t.emit("slidesLengthChange"),g.length!==v&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),x.length!==f&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!d&&!i.cssMode&&(i.effect==="slide"||i.effect==="fade")){const w=`${i.containerModifierClass}backface-hidden`,E=t.el.classList.contains(w);u<=i.maxBackfaceHiddenSlides?E||t.el.classList.add(w):E&&t.el.classList.remove(w)}}function Ei(t){const e=this,i=[],s=e.virtual&&e.params.virtual.enabled;let n=0,r;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const a=l=>s?e.slides[e.getSlideIndexByData(l)]:e.slides[l];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(l=>{i.push(l)});else for(r=0;r<Math.ceil(e.params.slidesPerView);r+=1){const l=e.activeIndex+r;if(l>e.slides.length&&!s)break;i.push(a(l))}else i.push(a(e.activeIndex));for(r=0;r<i.length;r+=1)if(typeof i[r]<"u"){const l=i[r].offsetHeight;n=l>n?l:n}(n||n===0)&&(e.wrapperEl.style.height=`${n}px`)}function Ti(){const t=this,e=t.slides,i=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(t.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-i-t.cssOverflowAdjustment()}const Ue=(t,e,i)=>{e&&!t.classList.contains(i)?t.classList.add(i):!e&&t.classList.contains(i)&&t.classList.remove(i)};function Ci(t){t===void 0&&(t=this&&this.translate||0);const e=this,i=e.params,{slides:s,rtlTranslate:n,snapGrid:r}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let a=-t;n&&(a=t),e.visibleSlidesIndexes=[],e.visibleSlides=[];let l=i.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*e.size:typeof l=="string"&&(l=parseFloat(l));for(let d=0;d<s.length;d+=1){const o=s[d];let c=o.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(c-=s[0].swiperSlideOffset);const u=(a+(i.centeredSlides?e.minTranslate():0)-c)/(o.swiperSlideSize+l),g=(a-r[0]+(i.centeredSlides?e.minTranslate():0)-c)/(o.swiperSlideSize+l),x=-(a-c),m=x+e.slidesSizesGrid[d],T=x>=0&&x<=e.size-e.slidesSizesGrid[d],P=x>=0&&x<e.size-1||m>1&&m<=e.size||x<=0&&m>=e.size;P&&(e.visibleSlides.push(o),e.visibleSlidesIndexes.push(d)),Ue(o,P,i.slideVisibleClass),Ue(o,T,i.slideFullyVisibleClass),o.progress=n?-u:u,o.originalProgress=n?-g:g}}function Pi(t){const e=this;if(typeof t>"u"){const c=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*c||0}const i=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:a,progressLoop:l}=e;const d=r,o=a;if(s===0)n=0,r=!0,a=!0;else{n=(t-e.minTranslate())/s;const c=Math.abs(t-e.minTranslate())<1,u=Math.abs(t-e.maxTranslate())<1;r=c||n<=0,a=u||n>=1,c&&(n=0),u&&(n=1)}if(i.loop){const c=e.getSlideIndexByData(0),u=e.getSlideIndexByData(e.slides.length-1),g=e.slidesGrid[c],x=e.slidesGrid[u],m=e.slidesGrid[e.slidesGrid.length-1],T=Math.abs(t);T>=g?l=(T-g)/m:l=(T+m-x)/m,l>1&&(l-=1)}Object.assign(e,{progress:n,progressLoop:l,isBeginning:r,isEnd:a}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&e.updateSlidesProgress(t),r&&!d&&e.emit("reachBeginning toEdge"),a&&!o&&e.emit("reachEnd toEdge"),(d&&!r||o&&!a)&&e.emit("fromEdge"),e.emit("progress",n)}const Ae=(t,e,i)=>{e&&!t.classList.contains(i)?t.classList.add(i):!e&&t.classList.contains(i)&&t.classList.remove(i)};function Mi(){const t=this,{slides:e,params:i,slidesEl:s,activeIndex:n}=t,r=t.virtual&&i.virtual.enabled,a=t.grid&&i.grid&&i.grid.rows>1,l=u=>X(s,`.${i.slideClass}${u}, swiper-slide${u}`)[0];let d,o,c;if(r)if(i.loop){let u=n-t.virtual.slidesBefore;u<0&&(u=t.virtual.slides.length+u),u>=t.virtual.slides.length&&(u-=t.virtual.slides.length),d=l(`[data-swiper-slide-index="${u}"]`)}else d=l(`[data-swiper-slide-index="${n}"]`);else a?(d=e.find(u=>u.column===n),c=e.find(u=>u.column===n+1),o=e.find(u=>u.column===n-1)):d=e[n];d&&(a||(c=pi(d,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!c&&(c=e[0]),o=fi(d,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!o===0&&(o=e[e.length-1]))),e.forEach(u=>{Ae(u,u===d,i.slideActiveClass),Ae(u,u===c,i.slideNextClass),Ae(u,u===o,i.slidePrevClass)}),t.emitSlidesClasses()}const ye=(t,e)=>{if(!t||t.destroyed||!t.params)return;const i=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,s=e.closest(i());if(s){let n=s.querySelector(`.${t.params.lazyPreloaderClass}`);!n&&t.isElement&&(s.shadowRoot?n=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(n=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),n&&n.remove())})),n&&n.remove()}},ze=(t,e)=>{if(!t.slides[e])return;const i=t.slides[e].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},Fe=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const i=t.slides.length;if(!i||!e||e<0)return;e=Math.min(e,i);const s=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),n=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const a=n,l=[a-e];l.push(...Array.from({length:e}).map((d,o)=>a+s+o)),t.slides.forEach((d,o)=>{l.includes(d.column)&&ze(t,o)});return}const r=n+s-1;if(t.params.rewind||t.params.loop)for(let a=n-e;a<=r+e;a+=1){const l=(a%i+i)%i;(l<n||l>r)&&ze(t,l)}else for(let a=Math.max(n-e,0);a<=Math.min(r+e,i-1);a+=1)a!==n&&(a>r||a<n)&&ze(t,a)};function Ii(t){const{slidesGrid:e,params:i}=t,s=t.rtlTranslate?t.translate:-t.translate;let n;for(let r=0;r<e.length;r+=1)typeof e[r+1]<"u"?s>=e[r]&&s<e[r+1]-(e[r+1]-e[r])/2?n=r:s>=e[r]&&s<e[r+1]&&(n=r+1):s>=e[r]&&(n=r);return i.normalizeSlideIndex&&(n<0||typeof n>"u")&&(n=0),n}function Oi(t){const e=this,i=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:n,activeIndex:r,realIndex:a,snapIndex:l}=e;let d=t,o;const c=x=>{let m=x-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof d>"u"&&(d=Ii(e)),s.indexOf(i)>=0)o=s.indexOf(i);else{const x=Math.min(n.slidesPerGroupSkip,d);o=x+Math.floor((d-x)/n.slidesPerGroup)}if(o>=s.length&&(o=s.length-1),d===r&&!e.params.loop){o!==l&&(e.snapIndex=o,e.emit("snapIndexChange"));return}if(d===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=c(d);return}const u=e.grid&&n.grid&&n.grid.rows>1;let g;if(e.virtual&&n.virtual.enabled&&n.loop)g=c(d);else if(u){const x=e.slides.find(T=>T.column===d);let m=parseInt(x.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(e.slides.indexOf(x),0)),g=Math.floor(m/n.grid.rows)}else if(e.slides[d]){const x=e.slides[d].getAttribute("data-swiper-slide-index");x?g=parseInt(x,10):g=d}else g=d;Object.assign(e,{previousSnapIndex:l,snapIndex:o,previousRealIndex:a,realIndex:g,previousIndex:r,activeIndex:d}),e.initialized&&Fe(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(a!==g&&e.emit("realIndexChange"),e.emit("slideChange"))}function Li(t,e){const i=this,s=i.params;let n=t.closest(`.${s.slideClass}, swiper-slide`);!n&&i.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(l=>{!n&&l.matches&&l.matches(`.${s.slideClass}, swiper-slide`)&&(n=l)});let r=!1,a;if(n){for(let l=0;l<i.slides.length;l+=1)if(i.slides[l]===n){r=!0,a=l;break}}if(n&&r)i.clickedSlide=n,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=a;else{i.clickedSlide=void 0,i.clickedIndex=void 0;return}s.slideToClickedSlide&&i.clickedIndex!==void 0&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}var ji={updateSize:bi,updateSlides:Si,updateAutoHeight:Ei,updateSlidesOffset:Ti,updateSlidesProgress:Ci,updateProgress:Pi,updateSlidesClasses:Mi,updateActiveIndex:Oi,updateClickedSlide:Li};function Ni(t){t===void 0&&(t=this.isHorizontal()?"x":"y");const e=this,{params:i,rtlTranslate:s,translate:n,wrapperEl:r}=e;if(i.virtualTranslate)return s?-n:n;if(i.cssMode)return n;let a=oi(r,t);return a+=e.cssOverflowAdjustment(),s&&(a=-a),a||0}function Ai(t,e){const i=this,{rtlTranslate:s,params:n,wrapperEl:r,progress:a}=i;let l=0,d=0;const o=0;i.isHorizontal()?l=s?-t:t:d=t,n.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d,n.cssMode?r[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-d:n.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():d-=i.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${d}px, ${o}px)`);let c;const u=i.maxTranslate()-i.minTranslate();u===0?c=0:c=(t-i.minTranslate())/u,c!==a&&i.updateProgress(t),i.emit("setTranslate",i.translate,e)}function zi(){return-this.snapGrid[0]}function Vi(){return-this.snapGrid[this.snapGrid.length-1]}function Ri(t,e,i,s,n){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),i===void 0&&(i=!0),s===void 0&&(s=!0);const r=this,{params:a,wrapperEl:l}=r;if(r.animating&&a.preventInteractionOnTransition)return!1;const d=r.minTranslate(),o=r.maxTranslate();let c;if(s&&t>d?c=d:s&&t<o?c=o:c=t,r.updateProgress(c),a.cssMode){const u=r.isHorizontal();if(e===0)l[u?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return lt({swiper:r,targetPosition:-c,side:u?"left":"top"}),!0;l.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return e===0?(r.setTransition(0),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(g){!r||r.destroyed||g.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,i&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var _i={getTranslate:Ni,setTranslate:Ai,minTranslate:zi,maxTranslate:Vi,translateTo:Ri};function Di(t,e){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${t}ms`,i.wrapperEl.style.transitionDelay=t===0?"0ms":""),i.emit("setTransition",t,e)}function pt(t){let{swiper:e,runCallbacks:i,direction:s,step:n}=t;const{activeIndex:r,previousIndex:a}=e;let l=s;l||(r>a?l="next":r<a?l="prev":l="reset"),e.emit(`transition${n}`),i&&l==="reset"?e.emit(`slideResetTransition${n}`):i&&r!==a&&(e.emit(`slideChangeTransition${n}`),l==="next"?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`))}function Fi(t,e){t===void 0&&(t=!0);const i=this,{params:s}=i;s.cssMode||(s.autoHeight&&i.updateAutoHeight(),pt({swiper:i,runCallbacks:t,direction:e,step:"Start"}))}function Bi(t,e){t===void 0&&(t=!0);const i=this,{params:s}=i;i.animating=!1,!s.cssMode&&(i.setTransition(0),pt({swiper:i,runCallbacks:t,direction:e,step:"End"}))}var $i={setTransition:Di,transitionStart:Fi,transitionEnd:Bi};function Gi(t,e,i,s,n){t===void 0&&(t=0),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const r=this;let a=t;a<0&&(a=0);const{params:l,snapGrid:d,slidesGrid:o,previousIndex:c,activeIndex:u,rtlTranslate:g,wrapperEl:x,enabled:m}=r;if(!m&&!s&&!n||r.destroyed||r.animating&&l.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=r.params.speed);const T=Math.min(r.params.slidesPerGroupSkip,a);let P=T+Math.floor((a-T)/r.params.slidesPerGroup);P>=d.length&&(P=d.length-1);const v=-d[P];if(l.normalizeSlideIndex)for(let A=0;A<o.length;A+=1){const y=-Math.floor(v*100),h=Math.floor(o[A]*100),w=Math.floor(o[A+1]*100);typeof o[A+1]<"u"?y>=h&&y<w-(w-h)/2?a=A:y>=h&&y<w&&(a=A+1):y>=h&&(a=A)}if(r.initialized&&a!==u&&(!r.allowSlideNext&&(g?v>r.translate&&v>r.minTranslate():v<r.translate&&v<r.minTranslate())||!r.allowSlidePrev&&v>r.translate&&v>r.maxTranslate()&&(u||0)!==a))return!1;a!==(c||0)&&i&&r.emit("beforeSlideChangeStart"),r.updateProgress(v);let f;a>u?f="next":a<u?f="prev":f="reset";const p=r.virtual&&r.params.virtual.enabled;if(!(p&&n)&&(g&&-v===r.translate||!g&&v===r.translate))return r.updateActiveIndex(a),l.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),l.effect!=="slide"&&r.setTranslate(v),f!=="reset"&&(r.transitionStart(i,f),r.transitionEnd(i,f)),!1;if(l.cssMode){const A=r.isHorizontal(),y=g?v:-v;if(e===0)p&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),p&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{x[A?"scrollLeft":"scrollTop"]=y})):x[A?"scrollLeft":"scrollTop"]=y,p&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return lt({swiper:r,targetPosition:y,side:A?"left":"top"}),!0;x.scrollTo({[A?"left":"top"]:y,behavior:"smooth"})}return!0}const L=ft().isSafari;return p&&!n&&L&&r.isElement&&r.virtual.update(!1,!1,a),r.setTransition(e),r.setTranslate(v),r.updateActiveIndex(a),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,s),r.transitionStart(i,f),e===0?r.transitionEnd(i,f):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(y){!r||r.destroyed||y.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(i,f))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function ki(t,e,i,s){t===void 0&&(t=0),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const n=this;if(n.destroyed)return;typeof e>"u"&&(e=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let a=t;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)a=a+n.virtual.slidesBefore;else{let l;if(r){const g=a*n.params.grid.rows;l=n.slides.find(x=>x.getAttribute("data-swiper-slide-index")*1===g).column}else l=n.getSlideIndexByData(a);const d=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:o}=n.params;let c=n.params.slidesPerView;c==="auto"?c=n.slidesPerViewDynamic():(c=Math.ceil(parseFloat(n.params.slidesPerView,10)),o&&c%2===0&&(c=c+1));let u=d-l<c;if(o&&(u=u||l<Math.ceil(c/2)),s&&o&&n.params.slidesPerView!=="auto"&&!r&&(u=!1),u){const g=o?l<n.activeIndex?"prev":"next":l-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:g,slideTo:!0,activeSlideIndex:g==="next"?l+1:l-d+1,slideRealIndex:g==="next"?n.realIndex:void 0})}if(r){const g=a*n.params.grid.rows;a=n.slides.find(x=>x.getAttribute("data-swiper-slide-index")*1===g).column}else a=n.getSlideIndexByData(a)}return requestAnimationFrame(()=>{n.slideTo(a,e,i,s)}),n}function Hi(t,e,i){e===void 0&&(e=!0);const s=this,{enabled:n,params:r,animating:a}=s;if(!n||s.destroyed)return s;typeof t>"u"&&(t=s.params.speed);let l=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(l=Math.max(s.slidesPerViewDynamic("current",!0),1));const d=s.activeIndex<r.slidesPerGroupSkip?1:l,o=s.virtual&&r.virtual.enabled;if(r.loop){if(a&&!o&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+d,t,e,i)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,t,e,i):s.slideTo(s.activeIndex+d,t,e,i)}function Wi(t,e,i){e===void 0&&(e=!0);const s=this,{params:n,snapGrid:r,slidesGrid:a,rtlTranslate:l,enabled:d,animating:o}=s;if(!d||s.destroyed)return s;typeof t>"u"&&(t=s.params.speed);const c=s.virtual&&n.virtual.enabled;if(n.loop){if(o&&!c&&n.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const u=l?s.translate:-s.translate;function g(f){return f<0?-Math.floor(Math.abs(f)):Math.floor(f)}const x=g(u),m=r.map(f=>g(f)),T=n.freeMode&&n.freeMode.enabled;let P=r[m.indexOf(x)-1];if(typeof P>"u"&&(n.cssMode||T)){let f;r.forEach((p,S)=>{x>=p&&(f=S)}),typeof f<"u"&&(P=T?r[f]:r[f>0?f-1:f])}let v=0;if(typeof P<"u"&&(v=a.indexOf(P),v<0&&(v=s.activeIndex-1),n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(v=v-s.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),n.rewind&&s.isBeginning){const f=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(f,t,e,i)}else if(n.loop&&s.activeIndex===0&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(v,t,e,i)}),!0;return s.slideTo(v,t,e,i)}function qi(t,e,i){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof t>"u"&&(t=s.params.speed),s.slideTo(s.activeIndex,t,e,i)}function Ui(t,e,i,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const n=this;if(n.destroyed)return;typeof t>"u"&&(t=n.params.speed);let r=n.activeIndex;const a=Math.min(n.params.slidesPerGroupSkip,r),l=a+Math.floor((r-a)/n.params.slidesPerGroup),d=n.rtlTranslate?n.translate:-n.translate;if(d>=n.snapGrid[l]){const o=n.snapGrid[l],c=n.snapGrid[l+1];d-o>(c-o)*s&&(r+=n.params.slidesPerGroup)}else{const o=n.snapGrid[l-1],c=n.snapGrid[l];d-o<=(c-o)*s&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,t,e,i)}function Yi(){const t=this;if(t.destroyed)return;const{params:e,slidesEl:i}=t,s=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let n=t.getSlideIndexWhenGrid(t.clickedIndex),r;const a=t.isElement?"swiper-slide":`.${e.slideClass}`,l=t.grid&&t.params.grid&&t.params.grid.rows>1;if(e.loop){if(t.animating)return;r=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?t.slideToLoop(r):n>(l?(t.slides.length-s)/2-(t.params.grid.rows-1):t.slides.length-s)?(t.loopFix(),n=t.getSlideIndex(X(i,`${a}[data-swiper-slide-index="${r}"]`)[0]),at(()=>{t.slideTo(n)})):t.slideTo(n)}else t.slideTo(n)}var Xi={slideTo:Gi,slideToLoop:ki,slideNext:Hi,slidePrev:Wi,slideReset:qi,slideToClosest:Ui,slideToClickedSlide:Yi};function Ki(t,e){const i=this,{params:s,slidesEl:n}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;const r=()=>{X(n,`.${s.slideClass}, swiper-slide`).forEach((x,m)=>{x.setAttribute("data-swiper-slide-index",m)})},a=()=>{const g=X(n,`.${s.slideBlankClass}`);g.forEach(x=>{x.remove()}),g.length>0&&(i.recalcSlides(),i.updateSlides())},l=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||l)&&a();const d=s.slidesPerGroup*(l?s.grid.rows:1),o=i.slides.length%d!==0,c=l&&i.slides.length%s.grid.rows!==0,u=g=>{for(let x=0;x<g;x+=1){const m=i.isElement?Te("swiper-slide",[s.slideBlankClass]):Te("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(m)}};if(o){if(s.loopAddBlankSlides){const g=d-i.slides.length%d;u(g),i.recalcSlides(),i.updateSlides()}else Ee("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(c){if(s.loopAddBlankSlides){const g=s.grid.rows-i.slides.length%s.grid.rows;u(g),i.recalcSlides(),i.updateSlides()}else Ee("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();i.loopFix({slideRealIndex:t,direction:s.centeredSlides?void 0:"next",initial:e})}function Qi(t){let{slideRealIndex:e,slideTo:i=!0,direction:s,setTranslate:n,activeSlideIndex:r,initial:a,byController:l,byMousewheel:d}=t===void 0?{}:t;const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:g,slidesEl:x,params:m}=o,{centeredSlides:T,initialSlide:P}=m;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&m.virtual.enabled){i&&(!m.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):m.centeredSlides&&o.snapIndex<m.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=u,o.allowSlideNext=g,o.emit("loopFix");return}let v=m.slidesPerView;v==="auto"?v=o.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),T&&v%2===0&&(v=v+1));const f=m.slidesPerGroupAuto?v:m.slidesPerGroup;let p=T?Math.max(f,Math.ceil(v/2)):f;p%f!==0&&(p+=f-p%f),p+=m.loopAdditionalSlides,o.loopedSlides=p;const S=o.grid&&m.grid&&m.grid.rows>1;c.length<v+p||o.params.effect==="cards"&&c.length<v+p*2?Ee("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&m.grid.fill==="row"&&Ee("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const C=[],L=[],A=S?Math.ceil(c.length/m.grid.rows):c.length,y=a&&A-P<v&&!T;let h=y?P:o.activeIndex;typeof r>"u"?r=o.getSlideIndex(c.find(j=>j.classList.contains(m.slideActiveClass))):h=r;const w=s==="next"||!s,E=s==="prev"||!s;let M=0,O=0;const V=(S?c[r].column:r)+(T&&typeof n>"u"?-v/2+.5:0);if(V<p){M=Math.max(p-V,f);for(let j=0;j<p-V;j+=1){const _=j-Math.floor(j/A)*A;if(S){const N=A-_-1;for(let z=c.length-1;z>=0;z-=1)c[z].column===N&&C.push(z)}else C.push(A-_-1)}}else if(V+v>A-p){O=Math.max(V-(A-p*2),f),y&&(O=Math.max(O,v-A+P+1));for(let j=0;j<O;j+=1){const _=j-Math.floor(j/A)*A;S?c.forEach((N,z)=>{N.column===_&&L.push(z)}):L.push(_)}}if(o.__preventObserver__=!0,requestAnimationFrame(()=>{o.__preventObserver__=!1}),o.params.effect==="cards"&&c.length<v+p*2&&(L.includes(r)&&L.splice(L.indexOf(r),1),C.includes(r)&&C.splice(C.indexOf(r),1)),E&&C.forEach(j=>{c[j].swiperLoopMoveDOM=!0,x.prepend(c[j]),c[j].swiperLoopMoveDOM=!1}),w&&L.forEach(j=>{c[j].swiperLoopMoveDOM=!0,x.append(c[j]),c[j].swiperLoopMoveDOM=!1}),o.recalcSlides(),m.slidesPerView==="auto"?o.updateSlides():S&&(C.length>0&&E||L.length>0&&w)&&o.slides.forEach((j,_)=>{o.grid.updateSlide(_,j,o.slides)}),m.watchSlidesProgress&&o.updateSlidesOffset(),i){if(C.length>0&&E){if(typeof e>"u"){const j=o.slidesGrid[h],N=o.slidesGrid[h+M]-j;d?o.setTranslate(o.translate-N):(o.slideTo(h+Math.ceil(M),0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-N,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-N))}else if(n){const j=S?C.length/m.grid.rows:C.length;o.slideTo(o.activeIndex+j,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(L.length>0&&w)if(typeof e>"u"){const j=o.slidesGrid[h],N=o.slidesGrid[h-O]-j;d?o.setTranslate(o.translate-N):(o.slideTo(h-O,0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-N,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-N))}else{const j=S?L.length/m.grid.rows:L.length;o.slideTo(o.activeIndex-j,0,!1,!0)}}if(o.allowSlidePrev=u,o.allowSlideNext=g,o.controller&&o.controller.control&&!l){const j={slideRealIndex:e,direction:s,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(_=>{!_.destroyed&&_.params.loop&&_.loopFix({...j,slideTo:_.params.slidesPerView===m.slidesPerView?i:!1})}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...j,slideTo:o.controller.control.params.slidesPerView===m.slidesPerView?i:!1})}o.emit("loopFix")}function Zi(){const t=this,{params:e,slidesEl:i}=t;if(!e.loop||!i||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const s=[];t.slides.forEach(n=>{const r=typeof n.swiperSlideIndex>"u"?n.getAttribute("data-swiper-slide-index")*1:n.swiperSlideIndex;s[r]=n}),t.slides.forEach(n=>{n.removeAttribute("data-swiper-slide-index")}),s.forEach(n=>{i.append(n)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}var Ji={loopCreate:Ki,loopFix:Qi,loopDestroy:Zi};function es(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const i=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function ts(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}var is={setGrabCursor:es,unsetGrabCursor:ts};function ss(t,e){e===void 0&&(e=this);function i(s){if(!s||s===K()||s===$())return null;s.assignedSlot&&(s=s.assignedSlot);const n=s.closest(t);return!n&&!s.getRootNode?null:n||i(s.getRootNode().host)}return i(e)}function Ye(t,e,i){const s=$(),{params:n}=t,r=n.edgeSwipeDetection,a=n.edgeSwipeThreshold;return r&&(i<=a||i>=s.innerWidth-a)?r==="prevent"?(e.preventDefault(),!0):!1:!0}function rs(t){const e=this,i=K();let s=t;s.originalEvent&&(s=s.originalEvent);const n=e.touchEventsData;if(s.type==="pointerdown"){if(n.pointerId!==null&&n.pointerId!==s.pointerId)return;n.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(n.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){Ye(e,s,s.targetTouches[0].pageX);return}const{params:r,touches:a,enabled:l}=e;if(!l||!r.simulateTouch&&s.pointerType==="mouse"||e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let d=s.target;if(r.touchEventsTarget==="wrapper"&&!ui(d,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||n.isTouched&&n.isMoved)return;const o=!!r.noSwipingClass&&r.noSwipingClass!=="",c=s.composedPath?s.composedPath():s.path;o&&s.target&&s.target.shadowRoot&&c&&(d=c[0]);const u=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,g=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(g?ss(u,d):d.closest(u))){e.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;a.currentX=s.pageX,a.currentY=s.pageY;const x=a.currentX,m=a.currentY;if(!Ye(e,s,x))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=x,a.startY=m,n.touchStartTime=Se(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let T=!0;d.matches(n.focusableElements)&&(T=!1,d.nodeName==="SELECT"&&(n.isTouched=!1)),i.activeElement&&i.activeElement.matches(n.focusableElements)&&i.activeElement!==d&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!d.matches(n.focusableElements))&&i.activeElement.blur();const P=T&&e.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||P)&&!d.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function ns(t){const e=K(),i=this,s=i.touchEventsData,{params:n,touches:r,rtlTranslate:a,enabled:l}=i;if(!l||!n.simulateTouch&&t.pointerType==="mouse")return;let d=t;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(s.touchId!==null||d.pointerId!==s.pointerId))return;let o;if(d.type==="touchmove"){if(o=[...d.changedTouches].find(C=>C.identifier===s.touchId),!o||o.identifier!==s.touchId)return}else o=d;if(!s.isTouched){s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",d);return}const c=o.pageX,u=o.pageY;if(d.preventedByNestedSwiper){r.startX=c,r.startY=u;return}if(!i.allowTouchMove){d.target.matches(s.focusableElements)||(i.allowClick=!1),s.isTouched&&(Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=Se());return}if(n.touchReleaseOnEdges&&!n.loop)if(i.isVertical()){if(u<r.startY&&i.translate<=i.maxTranslate()||u>r.startY&&i.translate>=i.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(a&&(c>r.startX&&-i.translate<=i.maxTranslate()||c<r.startX&&-i.translate>=i.minTranslate()))return;if(!a&&(c<r.startX&&i.translate<=i.maxTranslate()||c>r.startX&&i.translate>=i.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements)){s.isMoved=!0,i.allowClick=!1;return}s.allowTouchCallbacks&&i.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=u;const g=r.currentX-r.startX,x=r.currentY-r.startY;if(i.params.threshold&&Math.sqrt(g**2+x**2)<i.params.threshold)return;if(typeof s.isScrolling>"u"){let C;i.isHorizontal()&&r.currentY===r.startY||i.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:g*g+x*x>=25&&(C=Math.atan2(Math.abs(x),Math.abs(g))*180/Math.PI,s.isScrolling=i.isHorizontal()?C>n.touchAngle:90-C>n.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",d),typeof s.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(s.startMoving=!0),s.isScrolling||d.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;i.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let m=i.isHorizontal()?g:x,T=i.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(m=Math.abs(m)*(a?1:-1),T=Math.abs(T)*(a?1:-1)),r.diff=m,m*=n.touchRatio,a&&(m=-m,T=-T);const P=i.touchesDirection;i.swipeDirection=m>0?"prev":"next",i.touchesDirection=T>0?"prev":"next";const v=i.params.loop&&!n.cssMode,f=i.touchesDirection==="next"&&i.allowSlideNext||i.touchesDirection==="prev"&&i.allowSlidePrev;if(!s.isMoved){if(v&&f&&i.loopFix({direction:i.swipeDirection}),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const C=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});i.wrapperEl.dispatchEvent(C)}s.allowMomentumBounce=!1,n.grabCursor&&(i.allowSlideNext===!0||i.allowSlidePrev===!0)&&i.setGrabCursor(!0),i.emit("sliderFirstMove",d)}if(new Date().getTime(),n._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&P!==i.touchesDirection&&v&&f&&Math.abs(m)>=1){Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}i.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=m+s.startTranslate;let p=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),m>0?(v&&f&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-(n.slidesPerView!=="auto"&&i.slides.length-n.slidesPerView>=2?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>i.minTranslate()&&(p=!1,n.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+m)**S))):m<0&&(v&&f&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+(n.slidesPerView!=="auto"&&i.slides.length-n.slidesPerView>=2?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-(n.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<i.maxTranslate()&&(p=!1,n.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-m)**S))),p&&(d.preventedByNestedSwiper=!0),!i.allowSlideNext&&i.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&i.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&!i.allowSlideNext&&(s.currentTranslate=s.startTranslate),n.threshold>0)if(Math.abs(m)>n.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,r.diff=i.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{s.currentTranslate=s.startTranslate;return}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&i.freeMode||n.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function as(t){const e=this,i=e.touchEventsData;let s=t;s.originalEvent&&(s=s.originalEvent);let n;if(s.type==="touchend"||s.type==="touchcancel"){if(n=[...s.changedTouches].find(C=>C.identifier===i.touchId),!n||n.identifier!==i.touchId)return}else{if(i.touchId!==null||s.pointerId!==i.pointerId)return;n=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;i.pointerId=null,i.touchId=null;const{params:a,touches:l,rtlTranslate:d,slidesGrid:o,enabled:c}=e;if(!c||!a.simulateTouch&&s.pointerType==="mouse")return;if(i.allowTouchCallbacks&&e.emit("touchEnd",s),i.allowTouchCallbacks=!1,!i.isTouched){i.isMoved&&a.grabCursor&&e.setGrabCursor(!1),i.isMoved=!1,i.startMoving=!1;return}a.grabCursor&&i.isMoved&&i.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const u=Se(),g=u-i.touchStartTime;if(e.allowClick){const C=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(C&&C[0]||s.target,C),e.emit("tap click",s),g<300&&u-i.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(i.lastClickTime=Se(),at(()=>{e.destroyed||(e.allowClick=!0)}),!i.isTouched||!i.isMoved||!e.swipeDirection||l.diff===0&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset){i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;return}i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;let x;if(a.followFinger?x=d?e.translate:-e.translate:x=-i.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:x});return}const m=x>=-e.maxTranslate()&&!e.params.loop;let T=0,P=e.slidesSizesGrid[0];for(let C=0;C<o.length;C+=C<a.slidesPerGroupSkip?1:a.slidesPerGroup){const L=C<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;typeof o[C+L]<"u"?(m||x>=o[C]&&x<o[C+L])&&(T=C,P=o[C+L]-o[C]):(m||x>=o[C])&&(T=C,P=o[o.length-1]-o[o.length-2])}let v=null,f=null;a.rewind&&(e.isBeginning?f=a.virtual&&a.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(v=0));const p=(x-o[T])/P,S=T<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(g>a.longSwipesMs){if(!a.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(p>=a.longSwipesRatio?e.slideTo(a.rewind&&e.isEnd?v:T+S):e.slideTo(T)),e.swipeDirection==="prev"&&(p>1-a.longSwipesRatio?e.slideTo(T+S):f!==null&&p<0&&Math.abs(p)>a.longSwipesRatio?e.slideTo(f):e.slideTo(T))}else{if(!a.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(T+S):e.slideTo(T):(e.swipeDirection==="next"&&e.slideTo(v!==null?v:T+S),e.swipeDirection==="prev"&&e.slideTo(f!==null?f:T))}}function Xe(){const t=this,{params:e,el:i}=t;if(i&&i.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:n,snapGrid:r}=t,a=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const l=a&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!l?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!a?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=n,t.allowSlideNext=s,t.params.watchOverflow&&r!==t.snapGrid&&t.checkOverflow()}function ls(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function os(){const t=this,{wrapperEl:e,rtlTranslate:i,enabled:s}=t;if(!s)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let n;const r=t.maxTranslate()-t.minTranslate();r===0?n=0:n=(t.translate-t.minTranslate())/r,n!==t.progress&&t.updateProgress(i?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function ds(t){const e=this;ye(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function cs(){const t=this;t.documentTouchHandlerProceeded||(t.documentTouchHandlerProceeded=!0,t.params.touchReleaseOnEdges&&(t.el.style.touchAction="auto"))}const mt=(t,e)=>{const i=K(),{params:s,el:n,wrapperEl:r,device:a}=t,l=!!s.nested,d=e==="on"?"addEventListener":"removeEventListener",o=e;!n||typeof n=="string"||(i[d]("touchstart",t.onDocumentTouchStart,{passive:!1,capture:l}),n[d]("touchstart",t.onTouchStart,{passive:!1}),n[d]("pointerdown",t.onTouchStart,{passive:!1}),i[d]("touchmove",t.onTouchMove,{passive:!1,capture:l}),i[d]("pointermove",t.onTouchMove,{passive:!1,capture:l}),i[d]("touchend",t.onTouchEnd,{passive:!0}),i[d]("pointerup",t.onTouchEnd,{passive:!0}),i[d]("pointercancel",t.onTouchEnd,{passive:!0}),i[d]("touchcancel",t.onTouchEnd,{passive:!0}),i[d]("pointerout",t.onTouchEnd,{passive:!0}),i[d]("pointerleave",t.onTouchEnd,{passive:!0}),i[d]("contextmenu",t.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&n[d]("click",t.onClick,!0),s.cssMode&&r[d]("scroll",t.onScroll),s.updateOnWindowResize?t[o](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",Xe,!0):t[o]("observerUpdate",Xe,!0),n[d]("load",t.onLoad,{capture:!0}))};function us(){const t=this,{params:e}=t;t.onTouchStart=rs.bind(t),t.onTouchMove=ns.bind(t),t.onTouchEnd=as.bind(t),t.onDocumentTouchStart=cs.bind(t),e.cssMode&&(t.onScroll=os.bind(t)),t.onClick=ls.bind(t),t.onLoad=ds.bind(t),mt(t,"on")}function fs(){mt(this,"off")}var ps={attachEvents:us,detachEvents:fs};const Ke=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function ms(){const t=this,{realIndex:e,initialized:i,params:s,el:n}=t,r=s.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const a=K(),l=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",d=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?t.el:a.querySelector(s.breakpointsBase),o=t.getBreakpoint(r,l,d);if(!o||t.currentBreakpoint===o)return;const u=(o in r?r[o]:void 0)||t.originalParams,g=Ke(t,s),x=Ke(t,u),m=t.params.grabCursor,T=u.grabCursor,P=s.enabled;g&&!x?(n.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),t.emitContainerClasses()):!g&&x&&(n.classList.add(`${s.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&s.grid.fill==="column")&&n.classList.add(`${s.containerModifierClass}grid-column`),t.emitContainerClasses()),m&&!T?t.unsetGrabCursor():!m&&T&&t.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(L=>{if(typeof u[L]>"u")return;const A=s[L]&&s[L].enabled,y=u[L]&&u[L].enabled;A&&!y&&t[L].disable(),!A&&y&&t[L].enable()});const v=u.direction&&u.direction!==s.direction,f=s.loop&&(u.slidesPerView!==s.slidesPerView||v),p=s.loop;v&&i&&t.changeDirection(),k(t.params,u);const S=t.params.enabled,C=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),P&&!S?t.disable():!P&&S&&t.enable(),t.currentBreakpoint=o,t.emit("_beforeBreakpoint",u),i&&(f?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!p&&C?(t.loopCreate(e),t.updateSlides()):p&&!C&&t.loopDestroy()),t.emit("breakpoint",u)}function hs(t,e,i){if(e===void 0&&(e="window"),!t||e==="container"&&!i)return;let s=!1;const n=$(),r=e==="window"?n.innerHeight:i.clientHeight,a=Object.keys(t).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){const d=parseFloat(l.substr(1));return{value:r*d,point:l}}return{value:l,point:l}});a.sort((l,d)=>parseInt(l.value,10)-parseInt(d.value,10));for(let l=0;l<a.length;l+=1){const{point:d,value:o}=a[l];e==="window"?n.matchMedia(`(min-width: ${o}px)`).matches&&(s=d):o<=i.clientWidth&&(s=d)}return s||"max"}var gs={setBreakpoint:ms,getBreakpoint:hs};function vs(t,e){const i=[];return t.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(n=>{s[n]&&i.push(e+n)}):typeof s=="string"&&i.push(e+s)}),i}function ws(){const t=this,{classNames:e,params:i,rtl:s,el:n,device:r}=t,a=vs(["initialized",i.direction,{"free-mode":t.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&i.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);e.push(...a),n.classList.add(...e),t.emitContainerClasses()}function xs(){const t=this,{el:e,classNames:i}=t;!e||typeof e=="string"||(e.classList.remove(...i),t.emitContainerClasses())}var ys={addClasses:ws,removeClasses:xs};function bs(){const t=this,{isLocked:e,params:i}=t,{slidesOffsetBefore:s}=i;if(s){const n=t.slides.length-1,r=t.slidesGrid[n]+t.slidesSizesGrid[n]+s*2;t.isLocked=t.size>r}else t.isLocked=t.snapGrid.length===1;i.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),i.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}var Ss={checkOverflow:bs},Be={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Es(t,e){return function(s){s===void 0&&(s={});const n=Object.keys(s)[0],r=s[n];if(typeof r!="object"||r===null){k(e,s);return}if(t[n]===!0&&(t[n]={enabled:!0}),n==="navigation"&&t[n]&&t[n].enabled&&!t[n].prevEl&&!t[n].nextEl&&(t[n].auto=!0),["pagination","scrollbar"].indexOf(n)>=0&&t[n]&&t[n].enabled&&!t[n].el&&(t[n].auto=!0),!(n in t&&"enabled"in r)){k(e,s);return}typeof t[n]=="object"&&!("enabled"in t[n])&&(t[n].enabled=!0),t[n]||(t[n]={enabled:!1}),k(e,s)}}const Ve={eventsEmitter:yi,update:ji,translate:_i,transition:$i,slide:Xi,loop:Ji,grabCursor:is,events:ps,breakpoints:gs,checkOverflow:Ss,classes:ys},Re={};let ke=class ee{constructor(){let e,i;for(var s=arguments.length,n=new Array(s),r=0;r<s;r++)n[r]=arguments[r];n.length===1&&n[0].constructor&&Object.prototype.toString.call(n[0]).slice(8,-1)==="Object"?i=n[0]:[e,i]=n,i||(i={}),i=k({},i),e&&!i.el&&(i.el=e);const a=K();if(i.el&&typeof i.el=="string"&&a.querySelectorAll(i.el).length>1){const c=[];return a.querySelectorAll(i.el).forEach(u=>{const g=k({},i,{el:u});c.push(new ee(g))}),c}const l=this;l.__swiper__=!0,l.support=ct(),l.device=ut({userAgent:i.userAgent}),l.browser=ft(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],i.modules&&Array.isArray(i.modules)&&l.modules.push(...i.modules);const d={};l.modules.forEach(c=>{c({params:i,swiper:l,extendParams:Es(i,d),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});const o=k({},Be,d);return l.params=k({},o,Re,i),l.originalParams=k({},l.params),l.passedParams=k({},i),l.params&&l.params.on&&Object.keys(l.params.on).forEach(c=>{l.on(c,l.params.on[c])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:i,params:s}=this,n=X(i,`.${s.slideClass}, swiper-slide`),r=Ce(n[0]);return Ce(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(i=>i.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:i,params:s}=e;e.slides=X(i,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,i){const s=this;e=Math.min(Math.max(e,0),1);const n=s.minTranslate(),a=(s.maxTranslate()-n)*e+n;s.translateTo(a,typeof i>"u"?0:i),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",i.join(" "))}getSlideClasses(e){const i=this;return i.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(i.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=[];e.slides.forEach(s=>{const n=e.getSlideClasses(s);i.push({slideEl:s,classNames:n}),e.emit("_slideClass",s,n)}),e.emit("_slideClasses",i)}slidesPerViewDynamic(e,i){e===void 0&&(e="current"),i===void 0&&(i=!1);const s=this,{params:n,slides:r,slidesGrid:a,slidesSizesGrid:l,size:d,activeIndex:o}=s;let c=1;if(typeof n.slidesPerView=="number")return n.slidesPerView;if(n.centeredSlides){let u=r[o]?Math.ceil(r[o].swiperSlideSize):0,g;for(let x=o+1;x<r.length;x+=1)r[x]&&!g&&(u+=Math.ceil(r[x].swiperSlideSize),c+=1,u>d&&(g=!0));for(let x=o-1;x>=0;x-=1)r[x]&&!g&&(u+=r[x].swiperSlideSize,c+=1,u>d&&(g=!0))}else if(e==="current")for(let u=o+1;u<r.length;u+=1)(i?a[u]+l[u]-a[o]<d:a[u]-a[o]<d)&&(c+=1);else for(let u=o-1;u>=0;u-=1)a[o]-a[u]<d&&(c+=1);return c}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:i,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(a=>{a.complete&&ye(e,a)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function n(){const a=e.rtlTranslate?e.translate*-1:e.translate,l=Math.min(Math.max(a,e.maxTranslate()),e.minTranslate());e.setTranslate(l),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)n(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const a=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(a.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||n()}s.watchOverflow&&i!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,i){i===void 0&&(i=!0);const s=this,n=s.params.direction;return e||(e=n==="horizontal"?"vertical":"horizontal"),e===n||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${n}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(r=>{e==="vertical"?r.style.width="":r.style.height=""}),s.emit("changeDirection"),i&&s.update()),s}changeLanguageDirection(e){const i=this;i.rtl&&e==="rtl"||!i.rtl&&e==="ltr"||(i.rtl=e==="rtl",i.rtlTranslate=i.params.direction==="horizontal"&&i.rtl,i.rtl?(i.el.classList.add(`${i.params.containerModifierClass}rtl`),i.el.dir="rtl"):(i.el.classList.remove(`${i.params.containerModifierClass}rtl`),i.el.dir="ltr"),i.update())}mount(e){const i=this;if(i.mounted)return!0;let s=e||i.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=i,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===i.params.swiperElementNodeName.toUpperCase()&&(i.isElement=!0);const n=()=>`.${(i.params.wrapperClass||"").trim().split(" ").join(".")}`;let a=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(n()):X(s,n())[0];return!a&&i.params.createElements&&(a=Te("div",i.params.wrapperClass),s.append(a),X(s,`.${i.params.slideClass}`).forEach(l=>{a.append(l)})),Object.assign(i,{el:s,wrapperEl:a,slidesEl:i.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:a,hostEl:i.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||te(s,"direction")==="rtl",rtlTranslate:i.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||te(s,"direction")==="rtl"),wrongRTL:te(a,"display")==="-webkit-box"}),!0}init(e){const i=this;if(i.initialized||i.mount(e)===!1)return i;i.emit("beforeInit"),i.params.breakpoints&&i.setBreakpoint(),i.addClasses(),i.updateSize(),i.updateSlides(),i.params.watchOverflow&&i.checkOverflow(),i.params.grabCursor&&i.enabled&&i.setGrabCursor(),i.params.loop&&i.virtual&&i.params.virtual.enabled?i.slideTo(i.params.initialSlide+i.virtual.slidesBefore,0,i.params.runCallbacksOnInit,!1,!0):i.slideTo(i.params.initialSlide,0,i.params.runCallbacksOnInit,!1,!0),i.params.loop&&i.loopCreate(void 0,!0),i.attachEvents();const n=[...i.el.querySelectorAll('[loading="lazy"]')];return i.isElement&&n.push(...i.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach(r=>{r.complete?ye(i,r):r.addEventListener("load",a=>{ye(i,a.target)})}),Fe(i),i.initialized=!0,Fe(i),i.emit("init"),i.emit("afterInit"),i}destroy(e,i){e===void 0&&(e=!0),i===void 0&&(i=!0);const s=this,{params:n,el:r,wrapperEl:a,slides:l}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),n.loop&&s.loopDestroy(),i&&(s.removeClasses(),r&&typeof r!="string"&&r.removeAttribute("style"),a&&a.removeAttribute("style"),l&&l.length&&l.forEach(d=>{d.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(d=>{s.off(d)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),ai(s)),s.destroyed=!0),null}static extendDefaults(e){k(Re,e)}static get extendedDefaults(){return Re}static get defaults(){return Be}static installModule(e){ee.prototype.__modules__||(ee.prototype.__modules__=[]);const i=ee.prototype.__modules__;typeof e=="function"&&i.indexOf(e)<0&&i.push(e)}static use(e){return Array.isArray(e)?(e.forEach(i=>ee.installModule(i)),ee):(ee.installModule(e),ee)}};Object.keys(Ve).forEach(t=>{Object.keys(Ve[t]).forEach(e=>{ke.prototype[e]=Ve[t][e]})});ke.use([wi,xi]);const ht=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function ie(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"&&!t.__swiper__}function ae(t,e){const i=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>i.indexOf(s)<0).forEach(s=>{typeof t[s]>"u"?t[s]=e[s]:ie(e[s])&&ie(t[s])&&Object.keys(e[s]).length>0?e[s].__swiper__?t[s]=e[s]:ae(t[s],e[s]):t[s]=e[s]})}function gt(t){return t===void 0&&(t={}),t.navigation&&typeof t.navigation.nextEl>"u"&&typeof t.navigation.prevEl>"u"}function vt(t){return t===void 0&&(t={}),t.pagination&&typeof t.pagination.el>"u"}function wt(t){return t===void 0&&(t={}),t.scrollbar&&typeof t.scrollbar.el>"u"}function xt(t){t===void 0&&(t="");const e=t.split(" ").map(s=>s.trim()).filter(s=>!!s),i=[];return e.forEach(s=>{i.indexOf(s)<0&&i.push(s)}),i.join(" ")}function Ts(t){return t===void 0&&(t=""),t?t.includes("swiper-wrapper")?t:`swiper-wrapper ${t}`:"swiper-wrapper"}function Cs(t){let{swiper:e,slides:i,passedParams:s,changedParams:n,nextEl:r,prevEl:a,scrollbarEl:l,paginationEl:d}=t;const o=n.filter(h=>h!=="children"&&h!=="direction"&&h!=="wrapperClass"),{params:c,pagination:u,navigation:g,scrollbar:x,virtual:m,thumbs:T}=e;let P,v,f,p,S,C,L,A;n.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&!s.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(P=!0),n.includes("controller")&&s.controller&&s.controller.control&&c.controller&&!c.controller.control&&(v=!0),n.includes("pagination")&&s.pagination&&(s.pagination.el||d)&&(c.pagination||c.pagination===!1)&&u&&!u.el&&(f=!0),n.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||l)&&(c.scrollbar||c.scrollbar===!1)&&x&&!x.el&&(p=!0),n.includes("navigation")&&s.navigation&&(s.navigation.prevEl||a)&&(s.navigation.nextEl||r)&&(c.navigation||c.navigation===!1)&&g&&!g.prevEl&&!g.nextEl&&(S=!0);const y=h=>{e[h]&&(e[h].destroy(),h==="navigation"?(e.isElement&&(e[h].prevEl.remove(),e[h].nextEl.remove()),c[h].prevEl=void 0,c[h].nextEl=void 0,e[h].prevEl=void 0,e[h].nextEl=void 0):(e.isElement&&e[h].el.remove(),c[h].el=void 0,e[h].el=void 0))};n.includes("loop")&&e.isElement&&(c.loop&&!s.loop?C=!0:!c.loop&&s.loop?L=!0:A=!0),o.forEach(h=>{if(ie(c[h])&&ie(s[h]))Object.assign(c[h],s[h]),(h==="navigation"||h==="pagination"||h==="scrollbar")&&"enabled"in s[h]&&!s[h].enabled&&y(h);else{const w=s[h];(w===!0||w===!1)&&(h==="navigation"||h==="pagination"||h==="scrollbar")?w===!1&&y(h):c[h]=s[h]}}),o.includes("controller")&&!v&&e.controller&&e.controller.control&&c.controller&&c.controller.control&&(e.controller.control=c.controller.control),n.includes("children")&&i&&m&&c.virtual.enabled?(m.slides=i,m.update(!0)):n.includes("virtual")&&m&&c.virtual.enabled&&(i&&(m.slides=i),m.update(!0)),n.includes("children")&&i&&c.loop&&(A=!0),P&&T.init()&&T.update(!0),v&&(e.controller.control=c.controller.control),f&&(e.isElement&&(!d||typeof d=="string")&&(d=document.createElement("div"),d.classList.add("swiper-pagination"),d.part.add("pagination"),e.el.appendChild(d)),d&&(c.pagination.el=d),u.init(),u.render(),u.update()),p&&(e.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-scrollbar"),l.part.add("scrollbar"),e.el.appendChild(l)),l&&(c.scrollbar.el=l),x.init(),x.updateSize(),x.setTranslate()),S&&(e.isElement&&((!r||typeof r=="string")&&(r=document.createElement("div"),r.classList.add("swiper-button-next"),Pe(r,e.hostEl.constructor.nextButtonSvg),r.part.add("button-next"),e.el.appendChild(r)),(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-button-prev"),Pe(a,e.hostEl.constructor.prevButtonSvg),a.part.add("button-prev"),e.el.appendChild(a))),r&&(c.navigation.nextEl=r),a&&(c.navigation.prevEl=a),g.init(),g.update()),n.includes("allowSlideNext")&&(e.allowSlideNext=s.allowSlideNext),n.includes("allowSlidePrev")&&(e.allowSlidePrev=s.allowSlidePrev),n.includes("direction")&&e.changeDirection(s.direction,!1),(C||A)&&e.loopDestroy(),(L||A)&&e.loopCreate(),e.update()}function Ps(t,e){t===void 0&&(t={}),e===void 0&&(e=!0);const i={on:{}},s={},n={};ae(i,Be),i._emitClasses=!0,i.init=!1;const r={},a=ht.map(d=>d.replace(/_/,"")),l=Object.assign({},t);return Object.keys(l).forEach(d=>{typeof t[d]>"u"||(a.indexOf(d)>=0?ie(t[d])?(i[d]={},n[d]={},ae(i[d],t[d]),ae(n[d],t[d])):(i[d]=t[d],n[d]=t[d]):d.search(/on[A-Z]/)===0&&typeof t[d]=="function"?e?s[`${d[2].toLowerCase()}${d.substr(3)}`]=t[d]:i.on[`${d[2].toLowerCase()}${d.substr(3)}`]=t[d]:r[d]=t[d])}),["navigation","pagination","scrollbar"].forEach(d=>{i[d]===!0&&(i[d]={}),i[d]===!1&&delete i[d]}),{params:i,passedParams:n,rest:r,events:s}}function Ms(t,e){let{el:i,nextEl:s,prevEl:n,paginationEl:r,scrollbarEl:a,swiper:l}=t;gt(e)&&s&&n&&(l.params.navigation.nextEl=s,l.originalParams.navigation.nextEl=s,l.params.navigation.prevEl=n,l.originalParams.navigation.prevEl=n),vt(e)&&r&&(l.params.pagination.el=r,l.originalParams.pagination.el=r),wt(e)&&a&&(l.params.scrollbar.el=a,l.originalParams.scrollbar.el=a),l.init(i)}function Is(t,e,i,s,n){const r=[];if(!e)return r;const a=d=>{r.indexOf(d)<0&&r.push(d)};if(i&&s){const d=s.map(n),o=i.map(n);d.join("")!==o.join("")&&a("children"),s.length!==i.length&&a("children")}return ht.filter(d=>d[0]==="_").map(d=>d.replace(/_/,"")).forEach(d=>{if(d in t&&d in e)if(ie(t[d])&&ie(e[d])){const o=Object.keys(t[d]),c=Object.keys(e[d]);o.length!==c.length?a(d):(o.forEach(u=>{t[d][u]!==e[d][u]&&a(d)}),c.forEach(u=>{t[d][u]!==e[d][u]&&a(d)}))}else t[d]!==e[d]&&a(d)}),r}const Os=t=>{!t||t.destroyed||!t.params.virtual||t.params.virtual&&!t.params.virtual.enabled||(t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.emit("_virtualUpdated"),t.parallax&&t.params.parallax&&t.params.parallax.enabled&&t.parallax.setTranslate())};function Me(){return Me=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(t[s]=i[s])}return t},Me.apply(this,arguments)}function yt(t){return t.type&&t.type.displayName&&t.type.displayName.includes("SwiperSlide")}function bt(t){const e=[];return F.Children.toArray(t).forEach(i=>{yt(i)?e.push(i):i.props&&i.props.children&&bt(i.props.children).forEach(s=>e.push(s))}),e}function Ls(t){const e=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return F.Children.toArray(t).forEach(s=>{if(yt(s))e.push(s);else if(s.props&&s.props.slot&&i[s.props.slot])i[s.props.slot].push(s);else if(s.props&&s.props.children){const n=bt(s.props.children);n.length>0?n.forEach(r=>e.push(r)):i["container-end"].push(s)}else i["container-end"].push(s)}),{slides:e,slots:i}}function js(t,e,i){if(!i)return null;const s=c=>{let u=c;return c<0?u=e.length+c:u>=e.length&&(u=u-e.length),u},n=t.isHorizontal()?{[t.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:r,to:a}=i,l=t.params.loop?-e.length:0,d=t.params.loop?e.length*2:e.length,o=[];for(let c=l;c<d;c+=1)c>=r&&c<=a&&o.push(e[s(c)]);return o.map((c,u)=>F.cloneElement(c,{swiper:t,style:n,key:c.props.virtualIndex||c.key||`slide-${u}`}))}function me(t,e){return typeof window>"u"?I.useEffect(t,e):I.useLayoutEffect(t,e)}const Qe=I.createContext(null),Ns=I.createContext(null),St=I.forwardRef(function(t,e){let{className:i,tag:s="div",wrapperTag:n="div",children:r,onSwiper:a,...l}=t===void 0?{}:t,d=!1;const[o,c]=I.useState("swiper"),[u,g]=I.useState(null),[x,m]=I.useState(!1),T=I.useRef(!1),P=I.useRef(null),v=I.useRef(null),f=I.useRef(null),p=I.useRef(null),S=I.useRef(null),C=I.useRef(null),L=I.useRef(null),A=I.useRef(null),{params:y,passedParams:h,rest:w,events:E}=Ps(l),{slides:M,slots:O}=Ls(r),R=()=>{m(!x)};Object.assign(y.on,{_containerClasses(z,D){c(D)}});const V=()=>{Object.assign(y.on,E),d=!0;const z={...y};if(delete z.wrapperClass,v.current=new ke(z),v.current.virtual&&v.current.params.virtual.enabled){v.current.virtual.slides=M;const D={cache:!1,slides:M,renderExternal:g,renderExternalUpdate:!1};ae(v.current.params.virtual,D),ae(v.current.originalParams.virtual,D)}};P.current||V(),v.current&&v.current.on("_beforeBreakpoint",R);const j=()=>{d||!E||!v.current||Object.keys(E).forEach(z=>{v.current.on(z,E[z])})},_=()=>{!E||!v.current||Object.keys(E).forEach(z=>{v.current.off(z,E[z])})};I.useEffect(()=>()=>{v.current&&v.current.off("_beforeBreakpoint",R)}),I.useEffect(()=>{!T.current&&v.current&&(v.current.emitSlidesClasses(),T.current=!0)}),me(()=>{if(e&&(e.current=P.current),!!P.current)return v.current.destroyed&&V(),Ms({el:P.current,nextEl:S.current,prevEl:C.current,paginationEl:L.current,scrollbarEl:A.current,swiper:v.current},y),a&&!v.current.destroyed&&a(v.current),()=>{v.current&&!v.current.destroyed&&v.current.destroy(!0,!1)}},[]),me(()=>{j();const z=Is(h,f.current,M,p.current,D=>D.key);return f.current=h,p.current=M,z.length&&v.current&&!v.current.destroyed&&Cs({swiper:v.current,slides:M,passedParams:h,changedParams:z,nextEl:S.current,prevEl:C.current,scrollbarEl:A.current,paginationEl:L.current}),()=>{_()}}),me(()=>{Os(v.current)},[u]);function N(){return y.virtual?js(v.current,M,u):M.map((z,D)=>F.cloneElement(z,{swiper:v.current,swiperSlideIndex:D}))}return F.createElement(s,Me({ref:P,className:xt(`${o}${i?` ${i}`:""}`)},w),F.createElement(Ns.Provider,{value:v.current},O["container-start"],F.createElement(n,{className:Ts(y.wrapperClass)},O["wrapper-start"],N(),O["wrapper-end"]),gt(y)&&F.createElement(F.Fragment,null,F.createElement("div",{ref:C,className:"swiper-button-prev"}),F.createElement("div",{ref:S,className:"swiper-button-next"})),wt(y)&&F.createElement("div",{ref:A,className:"swiper-scrollbar"}),vt(y)&&F.createElement("div",{ref:L,className:"swiper-pagination"}),O["container-end"]))});St.displayName="Swiper";const Et=I.forwardRef(function(t,e){let{tag:i="div",children:s,className:n="",swiper:r,zoom:a,lazy:l,virtualIndex:d,swiperSlideIndex:o,...c}=t===void 0?{}:t;const u=I.useRef(null),[g,x]=I.useState("swiper-slide"),[m,T]=I.useState(!1);function P(S,C,L){C===u.current&&x(L)}me(()=>{if(typeof o<"u"&&(u.current.swiperSlideIndex=o),e&&(e.current=u.current),!(!u.current||!r)){if(r.destroyed){g!=="swiper-slide"&&x("swiper-slide");return}return r.on("_slideClass",P),()=>{r&&r.off("_slideClass",P)}}}),me(()=>{r&&u.current&&!r.destroyed&&x(r.getSlideClasses(u.current))},[r]);const v={isActive:g.indexOf("swiper-slide-active")>=0,isVisible:g.indexOf("swiper-slide-visible")>=0,isPrev:g.indexOf("swiper-slide-prev")>=0,isNext:g.indexOf("swiper-slide-next")>=0},f=()=>typeof s=="function"?s(v):s,p=()=>{T(!0)};return F.createElement(i,Me({ref:u,className:xt(`${g}${n?` ${n}`:""}`),"data-swiper-slide-index":d,onLoad:p},c),a&&F.createElement(Qe.Provider,{value:v},F.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":typeof a=="number"?a:void 0},f(),l&&!m&&F.createElement("div",{className:"swiper-lazy-preloader"}))),!a&&F.createElement(Qe.Provider,{value:v},f(),l&&!m&&F.createElement("div",{className:"swiper-lazy-preloader"})))});Et.displayName="SwiperSlide";function Tt(t,e,i,s){return t.params.createElements&&Object.keys(s).forEach(n=>{if(!i[n]&&i.auto===!0){let r=X(t.el,`.${s[n]}`)[0];r||(r=Te("div",s[n]),r.className=s[n],t.el.append(r)),i[n]=r,e[n]=r}}),i}function As(t){let{swiper:e,extendParams:i,on:s,emit:n}=t;i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function r(m){let T;return m&&typeof m=="string"&&e.isElement&&(T=e.el.querySelector(m)||e.hostEl.querySelector(m),T)?T:(m&&(typeof m=="string"&&(T=[...document.querySelectorAll(m)]),e.params.uniqueNavElements&&typeof m=="string"&&T&&T.length>1&&e.el.querySelectorAll(m).length===1?T=e.el.querySelector(m):T&&T.length===1&&(T=T[0])),m&&!T?m:T)}function a(m,T){const P=e.params.navigation;m=B(m),m.forEach(v=>{v&&(v.classList[T?"add":"remove"](...P.disabledClass.split(" ")),v.tagName==="BUTTON"&&(v.disabled=T),e.params.watchOverflow&&e.enabled&&v.classList[e.isLocked?"add":"remove"](P.lockClass))})}function l(){const{nextEl:m,prevEl:T}=e.navigation;if(e.params.loop){a(T,!1),a(m,!1);return}a(T,e.isBeginning&&!e.params.rewind),a(m,e.isEnd&&!e.params.rewind)}function d(m){m.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),n("navigationPrev"))}function o(m){m.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),n("navigationNext"))}function c(){const m=e.params.navigation;if(e.params.navigation=Tt(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(m.nextEl||m.prevEl))return;let T=r(m.nextEl),P=r(m.prevEl);Object.assign(e.navigation,{nextEl:T,prevEl:P}),T=B(T),P=B(P);const v=(f,p)=>{f&&f.addEventListener("click",p==="next"?o:d),!e.enabled&&f&&f.classList.add(...m.lockClass.split(" "))};T.forEach(f=>v(f,"next")),P.forEach(f=>v(f,"prev"))}function u(){let{nextEl:m,prevEl:T}=e.navigation;m=B(m),T=B(T);const P=(v,f)=>{v.removeEventListener("click",f==="next"?o:d),v.classList.remove(...e.params.navigation.disabledClass.split(" "))};m.forEach(v=>P(v,"next")),T.forEach(v=>P(v,"prev"))}s("init",()=>{e.params.navigation.enabled===!1?x():(c(),l())}),s("toEdge fromEdge lock unlock",()=>{l()}),s("destroy",()=>{u()}),s("enable disable",()=>{let{nextEl:m,prevEl:T}=e.navigation;if(m=B(m),T=B(T),e.enabled){l();return}[...m,...T].filter(P=>!!P).forEach(P=>P.classList.add(e.params.navigation.lockClass))}),s("click",(m,T)=>{let{nextEl:P,prevEl:v}=e.navigation;P=B(P),v=B(v);const f=T.target;let p=v.includes(f)||P.includes(f);if(e.isElement&&!p){const S=T.path||T.composedPath&&T.composedPath();S&&(p=S.find(C=>P.includes(C)||v.includes(C)))}if(e.params.navigation.hideOnClick&&!p){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===f||e.pagination.el.contains(f)))return;let S;P.length?S=P[0].classList.contains(e.params.navigation.hiddenClass):v.length&&(S=v[0].classList.contains(e.params.navigation.hiddenClass)),n(S===!0?"navigationShow":"navigationHide"),[...P,...v].filter(C=>!!C).forEach(C=>C.classList.toggle(e.params.navigation.hiddenClass))}});const g=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),c(),l()},x=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),u()};Object.assign(e.navigation,{enable:g,disable:x,update:l,init:c,destroy:u})}function ce(t){return t===void 0&&(t=""),`.${t.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function zs(t){let{swiper:e,extendParams:i,on:s,emit:n}=t;const r="swiper-pagination";i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:f=>f,formatFractionTotal:f=>f,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),e.pagination={el:null,bullets:[]};let a,l=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function o(f,p){const{bulletActiveClass:S}=e.params.pagination;f&&(f=f[`${p==="prev"?"previous":"next"}ElementSibling`],f&&(f.classList.add(`${S}-${p}`),f=f[`${p==="prev"?"previous":"next"}ElementSibling`],f&&f.classList.add(`${S}-${p}-${p}`)))}function c(f,p,S){if(f=f%S,p=p%S,p===f+1)return"next";if(p===f-1)return"previous"}function u(f){const p=f.target.closest(ce(e.params.pagination.bulletClass));if(!p)return;f.preventDefault();const S=Ce(p)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===S)return;const C=c(e.realIndex,S,e.slides.length);C==="next"?e.slideNext():C==="previous"?e.slidePrev():e.slideToLoop(S)}else e.slideTo(S)}function g(){const f=e.rtl,p=e.params.pagination;if(d())return;let S=e.pagination.el;S=B(S);let C,L;const A=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,y=e.params.loop?Math.ceil(A/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(L=e.previousRealIndex||0,C=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(C=e.snapIndex,L=e.previousSnapIndex):(L=e.previousIndex||0,C=e.activeIndex||0),p.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const h=e.pagination.bullets;let w,E,M;if(p.dynamicBullets&&(a=De(h[0],e.isHorizontal()?"width":"height"),S.forEach(O=>{O.style[e.isHorizontal()?"width":"height"]=`${a*(p.dynamicMainBullets+4)}px`}),p.dynamicMainBullets>1&&L!==void 0&&(l+=C-(L||0),l>p.dynamicMainBullets-1?l=p.dynamicMainBullets-1:l<0&&(l=0)),w=Math.max(C-l,0),E=w+(Math.min(h.length,p.dynamicMainBullets)-1),M=(E+w)/2),h.forEach(O=>{const R=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(V=>`${p.bulletActiveClass}${V}`)].map(V=>typeof V=="string"&&V.includes(" ")?V.split(" "):V).flat();O.classList.remove(...R)}),S.length>1)h.forEach(O=>{const R=Ce(O);R===C?O.classList.add(...p.bulletActiveClass.split(" ")):e.isElement&&O.setAttribute("part","bullet"),p.dynamicBullets&&(R>=w&&R<=E&&O.classList.add(...`${p.bulletActiveClass}-main`.split(" ")),R===w&&o(O,"prev"),R===E&&o(O,"next"))});else{const O=h[C];if(O&&O.classList.add(...p.bulletActiveClass.split(" ")),e.isElement&&h.forEach((R,V)=>{R.setAttribute("part",V===C?"bullet-active":"bullet")}),p.dynamicBullets){const R=h[w],V=h[E];for(let j=w;j<=E;j+=1)h[j]&&h[j].classList.add(...`${p.bulletActiveClass}-main`.split(" "));o(R,"prev"),o(V,"next")}}if(p.dynamicBullets){const O=Math.min(h.length,p.dynamicMainBullets+4),R=(a*O-a)/2-M*a,V=f?"right":"left";h.forEach(j=>{j.style[e.isHorizontal()?V:"top"]=`${R}px`})}}S.forEach((h,w)=>{if(p.type==="fraction"&&(h.querySelectorAll(ce(p.currentClass)).forEach(E=>{E.textContent=p.formatFractionCurrent(C+1)}),h.querySelectorAll(ce(p.totalClass)).forEach(E=>{E.textContent=p.formatFractionTotal(y)})),p.type==="progressbar"){let E;p.progressbarOpposite?E=e.isHorizontal()?"vertical":"horizontal":E=e.isHorizontal()?"horizontal":"vertical";const M=(C+1)/y;let O=1,R=1;E==="horizontal"?O=M:R=M,h.querySelectorAll(ce(p.progressbarFillClass)).forEach(V=>{V.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${R})`,V.style.transitionDuration=`${e.params.speed}ms`})}p.type==="custom"&&p.renderCustom?(Pe(h,p.renderCustom(e,C+1,y)),w===0&&n("paginationRender",h)):(w===0&&n("paginationRender",h),n("paginationUpdate",h)),e.params.watchOverflow&&e.enabled&&h.classList[e.isLocked?"add":"remove"](p.lockClass)})}function x(){const f=e.params.pagination;if(d())return;const p=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let S=e.pagination.el;S=B(S);let C="";if(f.type==="bullets"){let L=e.params.loop?Math.ceil(p/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&L>p&&(L=p);for(let A=0;A<L;A+=1)f.renderBullet?C+=f.renderBullet.call(e,A,f.bulletClass):C+=`<${f.bulletElement} ${e.isElement?'part="bullet"':""} class="${f.bulletClass}"></${f.bulletElement}>`}f.type==="fraction"&&(f.renderFraction?C=f.renderFraction.call(e,f.currentClass,f.totalClass):C=`<span class="${f.currentClass}"></span> / <span class="${f.totalClass}"></span>`),f.type==="progressbar"&&(f.renderProgressbar?C=f.renderProgressbar.call(e,f.progressbarFillClass):C=`<span class="${f.progressbarFillClass}"></span>`),e.pagination.bullets=[],S.forEach(L=>{f.type!=="custom"&&Pe(L,C||""),f.type==="bullets"&&e.pagination.bullets.push(...L.querySelectorAll(ce(f.bulletClass)))}),f.type!=="custom"&&n("paginationRender",S[0])}function m(){e.params.pagination=Tt(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const f=e.params.pagination;if(!f.el)return;let p;typeof f.el=="string"&&e.isElement&&(p=e.el.querySelector(f.el)),!p&&typeof f.el=="string"&&(p=[...document.querySelectorAll(f.el)]),p||(p=f.el),!(!p||p.length===0)&&(e.params.uniqueNavElements&&typeof f.el=="string"&&Array.isArray(p)&&p.length>1&&(p=[...e.el.querySelectorAll(f.el)],p.length>1&&(p=p.find(S=>dt(S,".swiper")[0]===e.el))),Array.isArray(p)&&p.length===1&&(p=p[0]),Object.assign(e.pagination,{el:p}),p=B(p),p.forEach(S=>{f.type==="bullets"&&f.clickable&&S.classList.add(...(f.clickableClass||"").split(" ")),S.classList.add(f.modifierClass+f.type),S.classList.add(e.isHorizontal()?f.horizontalClass:f.verticalClass),f.type==="bullets"&&f.dynamicBullets&&(S.classList.add(`${f.modifierClass}${f.type}-dynamic`),l=0,f.dynamicMainBullets<1&&(f.dynamicMainBullets=1)),f.type==="progressbar"&&f.progressbarOpposite&&S.classList.add(f.progressbarOppositeClass),f.clickable&&S.addEventListener("click",u),e.enabled||S.classList.add(f.lockClass)}))}function T(){const f=e.params.pagination;if(d())return;let p=e.pagination.el;p&&(p=B(p),p.forEach(S=>{S.classList.remove(f.hiddenClass),S.classList.remove(f.modifierClass+f.type),S.classList.remove(e.isHorizontal()?f.horizontalClass:f.verticalClass),f.clickable&&(S.classList.remove(...(f.clickableClass||"").split(" ")),S.removeEventListener("click",u))})),e.pagination.bullets&&e.pagination.bullets.forEach(S=>S.classList.remove(...f.bulletActiveClass.split(" ")))}s("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const f=e.params.pagination;let{el:p}=e.pagination;p=B(p),p.forEach(S=>{S.classList.remove(f.horizontalClass,f.verticalClass),S.classList.add(e.isHorizontal()?f.horizontalClass:f.verticalClass)})}),s("init",()=>{e.params.pagination.enabled===!1?v():(m(),x(),g())}),s("activeIndexChange",()=>{typeof e.snapIndex>"u"&&g()}),s("snapIndexChange",()=>{g()}),s("snapGridLengthChange",()=>{x(),g()}),s("destroy",()=>{T()}),s("enable disable",()=>{let{el:f}=e.pagination;f&&(f=B(f),f.forEach(p=>p.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),s("lock unlock",()=>{g()}),s("click",(f,p)=>{const S=p.target,C=B(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&C&&C.length>0&&!S.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&S===e.navigation.nextEl||e.navigation.prevEl&&S===e.navigation.prevEl))return;const L=C[0].classList.contains(e.params.pagination.hiddenClass);n(L===!0?"paginationShow":"paginationHide"),C.forEach(A=>A.classList.toggle(e.params.pagination.hiddenClass))}});const P=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:f}=e.pagination;f&&(f=B(f),f.forEach(p=>p.classList.remove(e.params.pagination.paginationDisabledClass))),m(),x(),g()},v=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:f}=e.pagination;f&&(f=B(f),f.forEach(p=>p.classList.add(e.params.pagination.paginationDisabledClass))),T()};Object.assign(e.pagination,{enable:P,disable:v,render:x,update:g,init:m,destroy:T})}function Vs(t){let{swiper:e,extendParams:i,on:s,emit:n,params:r}=t;e.autoplay={running:!1,paused:!1,timeLeft:0},i({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let a,l,d=r&&r.autoplay?r.autoplay.delay:3e3,o=r&&r.autoplay?r.autoplay.delay:3e3,c,u=new Date().getTime(),g,x,m,T,P,v,f;function p(N){!e||e.destroyed||!e.wrapperEl||N.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",p),!(f||N.detail&&N.detail.bySwiperTouchMove)&&w())}const S=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?g=!0:g&&(o=c,g=!1);const N=e.autoplay.paused?c:u+o-new Date().getTime();e.autoplay.timeLeft=N,n("autoplayTimeLeft",N,N/d),l=requestAnimationFrame(()=>{S()})},C=()=>{let N;return e.virtual&&e.params.virtual.enabled?N=e.slides.find(D=>D.classList.contains("swiper-slide-active")):N=e.slides[e.activeIndex],N?parseInt(N.getAttribute("data-swiper-autoplay"),10):void 0},L=N=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(l),S();let z=typeof N>"u"?e.params.autoplay.delay:N;d=e.params.autoplay.delay,o=e.params.autoplay.delay;const D=C();!Number.isNaN(D)&&D>0&&typeof N>"u"&&(z=D,d=D,o=D),c=z;const G=e.params.speed,H=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(G,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,G,!0,!0),n("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(G,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,G,!0,!0),n("autoplay")),e.params.cssMode&&(u=new Date().getTime(),requestAnimationFrame(()=>{L()})))};return z>0?(clearTimeout(a),a=setTimeout(()=>{H()},z)):requestAnimationFrame(()=>{H()}),z},A=()=>{u=new Date().getTime(),e.autoplay.running=!0,L(),n("autoplayStart")},y=()=>{e.autoplay.running=!1,clearTimeout(a),cancelAnimationFrame(l),n("autoplayStop")},h=(N,z)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(a),N||(v=!0);const D=()=>{n("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",p):w()};if(e.autoplay.paused=!0,z){P&&(c=e.params.autoplay.delay),P=!1,D();return}c=(c||e.params.autoplay.delay)-(new Date().getTime()-u),!(e.isEnd&&c<0&&!e.params.loop)&&(c<0&&(c=0),D())},w=()=>{e.isEnd&&c<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(u=new Date().getTime(),v?(v=!1,L(c)):L(),e.autoplay.paused=!1,n("autoplayResume"))},E=()=>{if(e.destroyed||!e.autoplay.running)return;const N=K();N.visibilityState==="hidden"&&(v=!0,h(!0)),N.visibilityState==="visible"&&w()},M=N=>{N.pointerType==="mouse"&&(v=!0,f=!0,!(e.animating||e.autoplay.paused)&&h(!0))},O=N=>{N.pointerType==="mouse"&&(f=!1,e.autoplay.paused&&w())},R=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",M),e.el.addEventListener("pointerleave",O))},V=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",M),e.el.removeEventListener("pointerleave",O))},j=()=>{K().addEventListener("visibilitychange",E)},_=()=>{K().removeEventListener("visibilitychange",E)};s("init",()=>{e.params.autoplay.enabled&&(R(),j(),A())}),s("destroy",()=>{V(),_(),e.autoplay.running&&y()}),s("_freeModeStaticRelease",()=>{(m||v)&&w()}),s("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?y():h(!0,!0)}),s("beforeTransitionStart",(N,z,D)=>{e.destroyed||!e.autoplay.running||(D||!e.params.autoplay.disableOnInteraction?h(!0,!0):y())}),s("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){y();return}x=!0,m=!1,v=!1,T=setTimeout(()=>{v=!0,m=!0,h(!0)},200)}}),s("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!x)){if(clearTimeout(T),clearTimeout(a),e.params.autoplay.disableOnInteraction){m=!1,x=!1;return}m&&e.params.cssMode&&w(),m=!1,x=!1}}),s("slideChange",()=>{e.destroyed||!e.autoplay.running||(P=!0)}),Object.assign(e.autoplay,{start:A,stop:y,pause:h,resume:w})}function Rs(t){const{effect:e,swiper:i,on:s,setTranslate:n,setTransition:r,overwriteParams:a,perspective:l,recreateShadows:d,getEffectParams:o}=t;s("beforeInit",()=>{if(i.params.effect!==e)return;i.classNames.push(`${i.params.containerModifierClass}${e}`),l&&l()&&i.classNames.push(`${i.params.containerModifierClass}3d`);const u=a?a():{};Object.assign(i.params,u),Object.assign(i.originalParams,u)}),s("setTranslate _virtualUpdated",()=>{i.params.effect===e&&n()}),s("setTransition",(u,g)=>{i.params.effect===e&&r(g)}),s("transitionEnd",()=>{if(i.params.effect===e&&d){if(!o||!o().slideShadows)return;i.slides.forEach(u=>{u.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(g=>g.remove())}),d()}});let c;s("virtualUpdate",()=>{i.params.effect===e&&(i.slides.length||(c=!0),requestAnimationFrame(()=>{c&&i.slides&&i.slides.length&&(n(),c=!1)}))})}function _s(t,e){const i=ot(e);return i!==e&&(i.style.backfaceVisibility="hidden",i.style["-webkit-backface-visibility"]="hidden"),i}function Ds(t){let{swiper:e,duration:i,transformElements:s}=t;const{activeIndex:n}=e;if(e.params.virtualTranslate&&i!==0){let r=!1,a;a=s,a.forEach(l=>{mi(l,()=>{if(r||!e||e.destroyed)return;r=!0,e.animating=!1;const d=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(d)})})}}function Fs(t){let{swiper:e,extendParams:i,on:s}=t;i({fadeEffect:{crossFade:!1}}),Rs({effect:"fade",swiper:e,on:s,setTranslate:()=>{const{slides:a}=e,l=e.params.fadeEffect;for(let d=0;d<a.length;d+=1){const o=e.slides[d];let u=-o.swiperSlideOffset;e.params.virtualTranslate||(u-=e.translate);let g=0;e.isHorizontal()||(g=u,u=0);const x=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(o.progress),0):1+Math.min(Math.max(o.progress,-1),0),m=_s(l,o);m.style.opacity=x,m.style.transform=`translate3d(${u}px, ${g}px, 0px)`}},setTransition:a=>{const l=e.slides.map(d=>ot(d));l.forEach(d=>{d.style.transitionDuration=`${a}ms`}),Ds({swiper:e,duration:a,transformElements:l})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function Bs({services:t=[]}){const e=I.useRef(null),i={ScaleIcon:be,DocumentTextIcon:st,UserGroupIcon:it,ShieldCheckIcon:tt,HomeIcon:_t,HeartIcon:Vt,BriefcaseIcon:zt,BuildingOfficeIcon:At},s=t.map(n=>({...n,icon:i[n.icon]||be}));return s.length?b.jsxs("section",{className:"py-16 bg-gradient-to-br from-secondary-50 to-primary-50 relative overflow-hidden",children:[b.jsxs("div",{className:"absolute inset-0 opacity-5",children:[b.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-primary-600 rounded-full blur-3xl"}),b.jsx("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-secondary-600 rounded-full blur-3xl"})]}),b.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:[b.jsxs(q.div,{className:"text-center mb-12",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[b.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold text-secondary-900 mb-4",children:"Our Legal Services"}),b.jsx("p",{className:"text-lg text-secondary-600 max-w-2xl mx-auto",children:"Comprehensive legal solutions tailored to meet your specific needs with professional expertise and personalized attention."})]}),b.jsxs("div",{className:"relative",children:[b.jsx(St,{ref:e,modules:[As,zs,Vs,Fs],spaceBetween:30,slidesPerView:1,navigation:{nextEl:".swiper-button-next-custom",prevEl:".swiper-button-prev-custom"},pagination:{clickable:!0,bulletClass:"swiper-pagination-bullet-custom",bulletActiveClass:"swiper-pagination-bullet-active-custom"},autoplay:{delay:5e3,disableOnInteraction:!1},loop:s.length>1,breakpoints:{640:{slidesPerView:1},768:{slidesPerView:2},1024:{slidesPerView:3}},className:"service-slider",children:s.map((n,r)=>b.jsx(Et,{children:b.jsx($s,{service:n,index:r})},n.id||r))}),b.jsx("button",{className:"swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-primary-600 hover:bg-primary-600 hover:text-white transition-all duration-300 -ml-6",children:b.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:b.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),b.jsx("button",{className:"swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-primary-600 hover:bg-primary-600 hover:text-white transition-all duration-300 -mr-6",children:b.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:b.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),b.jsx(q.div,{className:"text-center mt-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:b.jsxs(pe,{href:"/services",className:"inline-flex items-center px-8 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:["View All Services",b.jsx($e,{className:"ml-2 h-5 w-5"})]})})]}),b.jsx("style",{jsx:!0,children:`
                .service-slider .swiper-pagination {
                    bottom: -50px;
                }
                .swiper-pagination-bullet-custom {
                    width: 12px;
                    height: 12px;
                    background: #cbd5e1;
                    opacity: 1;
                    margin: 0 6px;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                }
                .swiper-pagination-bullet-active-custom {
                    background: #2563eb;
                    transform: scale(1.2);
                }
            `})]}):null}function $s({service:t,index:e}){const i=t.icon;return b.jsxs(q.div,{className:"bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group h-full",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:e*.1},viewport:{once:!0},whileHover:{y:-10},children:[b.jsxs("div",{className:"relative h-48 overflow-hidden",children:[t.image_url?b.jsx("img",{src:`/storage/${t.image_url}`,alt:t.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"}):b.jsx("div",{className:"w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center",children:b.jsx(i,{className:"h-16 w-16 text-primary-600"})}),b.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),b.jsxs("div",{className:"p-6",children:[b.jsxs("div",{className:"flex items-center mb-4",children:[b.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-primary-600 transition-colors duration-300",children:b.jsx(i,{className:"h-6 w-6 text-primary-600 group-hover:text-white transition-colors duration-300"})}),b.jsx("h3",{className:"text-xl font-semibold text-secondary-900 group-hover:text-primary-600 transition-colors duration-300",children:t.title})]}),b.jsx("p",{className:"text-secondary-600 mb-4 line-clamp-3",children:t.description}),t.features&&t.features.length>0&&b.jsxs("ul",{className:"space-y-2 mb-6",children:[t.features.slice(0,3).map((s,n)=>b.jsxs("li",{className:"flex items-start text-sm text-secondary-700",children:[b.jsx(Rt,{className:"h-4 w-4 text-primary-600 mr-2 flex-shrink-0 mt-0.5"}),b.jsx("span",{className:"line-clamp-1",children:s})]},n)),t.features.length>3&&b.jsxs("li",{className:"text-sm text-primary-600 font-medium",children:["+",t.features.length-3," more features"]})]}),b.jsxs(pe,{href:"/contact",className:"inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm group-hover:translate-x-1 transition-all duration-300",children:["Learn More",b.jsx($e,{className:"ml-1 h-4 w-4"})]})]})]})}var ue={},fe={exports:{}},Gs=fe.exports,Ze;function ks(){return Ze||(Ze=1,function(t,e){(function(i,s){s(e)})(Gs,function(i){var s=function(){return s=Object.assign||function(r){for(var a,l=1,d=arguments.length;l<d;l++)for(var o in a=arguments[l])Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o]);return r},s.apply(this,arguments)},n=function(){function r(a,l,d){var o=this;this.endVal=l,this.options=d,this.version="2.9.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(c){o.startTime||(o.startTime=c);var u=c-o.startTime;o.remaining=o.duration-u,o.useEasing?o.countDown?o.frameVal=o.startVal-o.easingFn(u,0,o.startVal-o.endVal,o.duration):o.frameVal=o.easingFn(u,o.startVal,o.endVal-o.startVal,o.duration):o.frameVal=o.startVal+(o.endVal-o.startVal)*(u/o.duration);var g=o.countDown?o.frameVal<o.endVal:o.frameVal>o.endVal;o.frameVal=g?o.endVal:o.frameVal,o.frameVal=Number(o.frameVal.toFixed(o.options.decimalPlaces)),o.printValue(o.frameVal),u<o.duration?o.rAF=requestAnimationFrame(o.count):o.finalEndVal!==null?o.update(o.finalEndVal):o.options.onCompleteCallback&&o.options.onCompleteCallback()},this.formatNumber=function(c){var u,g,x,m,T=c<0?"-":"";u=Math.abs(c).toFixed(o.options.decimalPlaces);var P=(u+="").split(".");if(g=P[0],x=P.length>1?o.options.decimal+P[1]:"",o.options.useGrouping){m="";for(var v=3,f=0,p=0,S=g.length;p<S;++p)o.options.useIndianSeparators&&p===4&&(v=2,f=1),p!==0&&f%v==0&&(m=o.options.separator+m),f++,m=g[S-p-1]+m;g=m}return o.options.numerals&&o.options.numerals.length&&(g=g.replace(/[0-9]/g,function(C){return o.options.numerals[+C]}),x=x.replace(/[0-9]/g,function(C){return o.options.numerals[+C]})),T+o.options.prefix+g+x+o.options.suffix},this.easeOutExpo=function(c,u,g,x){return g*(1-Math.pow(2,-10*c/x))*1024/1023+u},this.options=s(s({},this.defaults),d),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.el=typeof a=="string"?document.getElementById(a):a,l=l??this.parse(this.el.innerHTML),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(l),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,this.options.separator===""&&(this.options.useGrouping=!1),this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined",typeof window<"u"&&this.options.enableScrollSpy&&(this.error?console.error(this.error,a):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return o.handleScroll(o)}),window.onscroll=function(){window.onScrollFns.forEach(function(c){return c()})},this.handleScroll(this)))}return r.prototype.handleScroll=function(a){if(a&&window&&!a.once){var l=window.innerHeight+window.scrollY,d=a.el.getBoundingClientRect(),o=d.top+window.pageYOffset,c=d.top+d.height+window.pageYOffset;c<l&&c>window.scrollY&&a.paused?(a.paused=!1,setTimeout(function(){return a.start()},a.options.scrollSpyDelay),a.options.scrollSpyOnce&&(a.once=!0)):(window.scrollY>c||o>l)&&!a.paused&&a.reset()}},r.prototype.determineDirectionAndSmartEasing=function(){var a=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>a;var l=a-this.startVal;if(Math.abs(l)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=a;var d=this.countDown?1:-1;this.endVal=a+d*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=a,this.finalEndVal=null;this.finalEndVal!==null?this.useEasing=!1:this.useEasing=this.options.useEasing},r.prototype.start=function(a){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),a&&(this.options.onCompleteCallback=a),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},r.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},r.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},r.prototype.update=function(a){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(a),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,this.finalEndVal==null&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},r.prototype.printValue=function(a){var l;if(this.el){var d=this.formattingFn(a);!((l=this.options.plugin)===null||l===void 0)&&l.render?this.options.plugin.render(this.el,d):this.el.tagName==="INPUT"?this.el.value=d:this.el.tagName==="text"||this.el.tagName==="tspan"?this.el.textContent=d:this.el.innerHTML=d}},r.prototype.ensureNumber=function(a){return typeof a=="number"&&!isNaN(a)},r.prototype.validateValue=function(a){var l=Number(a);return this.ensureNumber(l)?l:(this.error="[CountUp] invalid start or end value: ".concat(a),null)},r.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},r.prototype.parse=function(a){var l=function(u){return u.replace(/([.,'  ])/g,"\\$1")},d=l(this.options.separator),o=l(this.options.decimal),c=a.replace(new RegExp(d,"g"),"").replace(new RegExp(o,"g"),".");return parseFloat(c)},r}();i.CountUp=n})}(fe,fe.exports)),fe.exports}var Je;function Hs(){if(Je)return ue;Je=1,Object.defineProperty(ue,"__esModule",{value:!0});var t=Ct(),e=ks();function i(y,h){var w=y==null?null:typeof Symbol<"u"&&y[Symbol.iterator]||y["@@iterator"];if(w!=null){var E,M,O,R,V=[],j=!0,_=!1;try{if(O=(w=w.call(y)).next,h!==0)for(;!(j=(E=O.call(w)).done)&&(V.push(E.value),V.length!==h);j=!0);}catch(N){_=!0,M=N}finally{try{if(!j&&w.return!=null&&(R=w.return(),Object(R)!==R))return}finally{if(_)throw M}}return V}}function s(y,h){var w=Object.keys(y);if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(y);h&&(E=E.filter(function(M){return Object.getOwnPropertyDescriptor(y,M).enumerable})),w.push.apply(w,E)}return w}function n(y){for(var h=1;h<arguments.length;h++){var w=arguments[h]!=null?arguments[h]:{};h%2?s(Object(w),!0).forEach(function(E){l(y,E,w[E])}):Object.getOwnPropertyDescriptors?Object.defineProperties(y,Object.getOwnPropertyDescriptors(w)):s(Object(w)).forEach(function(E){Object.defineProperty(y,E,Object.getOwnPropertyDescriptor(w,E))})}return y}function r(y,h){if(typeof y!="object"||!y)return y;var w=y[Symbol.toPrimitive];if(w!==void 0){var E=w.call(y,h);if(typeof E!="object")return E;throw new TypeError("@@toPrimitive must return a primitive value.")}return(h==="string"?String:Number)(y)}function a(y){var h=r(y,"string");return typeof h=="symbol"?h:String(h)}function l(y,h,w){return h=a(h),h in y?Object.defineProperty(y,h,{value:w,enumerable:!0,configurable:!0,writable:!0}):y[h]=w,y}function d(){return d=Object.assign?Object.assign.bind():function(y){for(var h=1;h<arguments.length;h++){var w=arguments[h];for(var E in w)Object.prototype.hasOwnProperty.call(w,E)&&(y[E]=w[E])}return y},d.apply(this,arguments)}function o(y,h){if(y==null)return{};var w={},E=Object.keys(y),M,O;for(O=0;O<E.length;O++)M=E[O],!(h.indexOf(M)>=0)&&(w[M]=y[M]);return w}function c(y,h){if(y==null)return{};var w=o(y,h),E,M;if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(y);for(M=0;M<O.length;M++)E=O[M],!(h.indexOf(E)>=0)&&Object.prototype.propertyIsEnumerable.call(y,E)&&(w[E]=y[E])}return w}function u(y,h){return g(y)||i(y,h)||x(y,h)||T()}function g(y){if(Array.isArray(y))return y}function x(y,h){if(y){if(typeof y=="string")return m(y,h);var w=Object.prototype.toString.call(y).slice(8,-1);if(w==="Object"&&y.constructor&&(w=y.constructor.name),w==="Map"||w==="Set")return Array.from(y);if(w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w))return m(y,h)}}function m(y,h){(h==null||h>y.length)&&(h=y.length);for(var w=0,E=new Array(h);w<h;w++)E[w]=y[w];return E}function T(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var P=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?t.useLayoutEffect:t.useEffect;function v(y){var h=t.useRef(y);return P(function(){h.current=y}),t.useCallback(function(){for(var w=arguments.length,E=new Array(w),M=0;M<w;M++)E[M]=arguments[M];return h.current.apply(void 0,E)},[])}var f=function(h,w){var E=w.decimal,M=w.decimals,O=w.duration,R=w.easingFn,V=w.end,j=w.formattingFn,_=w.numerals,N=w.prefix,z=w.separator,D=w.start,G=w.suffix,H=w.useEasing,Q=w.useGrouping,U=w.useIndianSeparators,se=w.enableScrollSpy,W=w.scrollSpyDelay,re=w.scrollSpyOnce,Z=w.plugin;return new e.CountUp(h,V,{startVal:D,duration:O,decimal:E,decimalPlaces:M,easingFn:R,formattingFn:j,numerals:_,separator:z,prefix:N,suffix:G,plugin:Z,useEasing:H,useIndianSeparators:U,useGrouping:Q,enableScrollSpy:se,scrollSpyDelay:W,scrollSpyOnce:re})},p=["ref","startOnMount","enableReinitialize","delay","onEnd","onStart","onPauseResume","onReset","onUpdate"],S={decimal:".",separator:",",delay:null,prefix:"",suffix:"",duration:2,start:0,decimals:0,startOnMount:!0,enableReinitialize:!0,useEasing:!0,useGrouping:!0,useIndianSeparators:!1},C=function(h){var w=Object.fromEntries(Object.entries(h).filter(function(Y){var de=u(Y,2),he=de[1];return he!==void 0})),E=t.useMemo(function(){return n(n({},S),w)},[h]),M=E.ref,O=E.startOnMount,R=E.enableReinitialize,V=E.delay,j=E.onEnd,_=E.onStart,N=E.onPauseResume,z=E.onReset,D=E.onUpdate,G=c(E,p),H=t.useRef(),Q=t.useRef(),U=t.useRef(!1),se=v(function(){return f(typeof M=="string"?M:M.current,G)}),W=v(function(Y){var de=H.current;if(de&&!Y)return de;var he=se();return H.current=he,he}),re=v(function(){var Y=function(){return W(!0).start(function(){j==null||j({pauseResume:Z,reset:J,start:oe,update:le})})};V&&V>0?Q.current=setTimeout(Y,V*1e3):Y(),_==null||_({pauseResume:Z,reset:J,update:le})}),Z=v(function(){W().pauseResume(),N==null||N({reset:J,start:oe,update:le})}),J=v(function(){W().el&&(Q.current&&clearTimeout(Q.current),W().reset(),z==null||z({pauseResume:Z,start:oe,update:le}))}),le=v(function(Y){W().update(Y),D==null||D({pauseResume:Z,reset:J,start:oe})}),oe=v(function(){J(),re()}),Ie=v(function(Y){O&&(Y&&J(),re())});return t.useEffect(function(){U.current?R&&Ie(!0):(U.current=!0,Ie())},[R,U,Ie,V,h.start,h.suffix,h.prefix,h.duration,h.separator,h.decimals,h.decimal,h.formattingFn]),t.useEffect(function(){return function(){J()}},[J]),{start:oe,pauseResume:Z,reset:J,update:le,getCountUp:W}},L=["className","redraw","containerProps","children","style"],A=function(h){var w=h.className,E=h.redraw,M=h.containerProps,O=h.children,R=h.style,V=c(h,L),j=t.useRef(null),_=t.useRef(!1),N=C(n(n({},V),{},{ref:j,startOnMount:typeof O!="function"||h.delay===0,enableReinitialize:!1})),z=N.start,D=N.reset,G=N.update,H=N.pauseResume,Q=N.getCountUp,U=v(function(){z()}),se=v(function(Z){h.preserveValue||D(),G(Z)}),W=v(function(){if(typeof h.children=="function"&&!(j.current instanceof Element)){console.error(`Couldn't find attached element to hook the CountUp instance into! Try to attach "containerRef" from the render prop to a an Element, eg. <span ref={containerRef} />.`);return}Q()});t.useEffect(function(){W()},[W]),t.useEffect(function(){_.current&&se(h.end)},[h.end,se]);var re=E&&h;return t.useEffect(function(){E&&_.current&&U()},[U,E,re]),t.useEffect(function(){!E&&_.current&&U()},[U,E,h.start,h.suffix,h.prefix,h.duration,h.separator,h.decimals,h.decimal,h.className,h.formattingFn]),t.useEffect(function(){_.current=!0},[]),typeof O=="function"?O({countUpRef:j,start:z,reset:D,update:G,pauseResume:H,getCountUp:Q}):t.createElement("span",d({className:w,ref:j,style:R},M),typeof h.start<"u"?Q().formattingFn(h.start):"")};return ue.default=A,ue.useCountUp=C,ue}var Ws=Hs();const qs=Pt(Ws);function xe({end:t,duration:e=2,suffix:i="",prefix:s="",title:n,description:r}){const{ref:a,inView:l}=si({threshold:.3,triggerOnce:!0});return b.jsxs(q.div,{ref:a,className:"text-center",initial:{opacity:0,y:30},animate:l?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[b.jsx("div",{className:"text-4xl md:text-5xl font-bold text-white mb-2",children:l&&b.jsxs(b.Fragment,{children:[s,b.jsx(qs,{start:0,end:t,duration:e,separator:","}),i]})}),b.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:n}),b.jsx("p",{className:"text-primary-100",children:r})]})}function Us({testimonials:t}){const[e,i]=I.useState(0);I.useEffect(()=>{const a=setInterval(()=>{i(l=>l===t.length-1?0:l+1)},5e3);return()=>clearInterval(a)},[t.length]);const s=()=>{i(e===0?t.length-1:e-1)},n=()=>{i(e===t.length-1?0:e+1)},r=a=>{i(a)};return b.jsxs("div",{className:"relative max-w-4xl mx-auto",children:[b.jsx("div",{className:"relative h-64 overflow-hidden",children:b.jsx(Ut,{mode:"wait",children:b.jsx(q.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:-50},transition:{duration:.5},className:"absolute inset-0 flex items-center justify-center",children:b.jsxs("div",{className:"text-center px-8",children:[b.jsx("div",{className:"flex justify-center mb-4",children:[...Array(t[e].rating)].map((a,l)=>b.jsx("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:b.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},l))}),b.jsxs("blockquote",{className:"text-lg md:text-xl text-secondary-700 mb-6 italic",children:['"',t[e].content,'"']}),b.jsxs("div",{className:"flex items-center justify-center",children:[b.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:b.jsx("span",{className:"text-primary-600 font-semibold text-lg",children:t[e].name.charAt(0)})}),b.jsxs("div",{className:"text-left",children:[b.jsx("p",{className:"font-semibold text-secondary-900",children:t[e].name}),b.jsx("p",{className:"text-sm text-secondary-500",children:t[e].role})]})]})]})},e)})}),b.jsx("button",{onClick:s,className:"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200 text-secondary-600 hover:text-primary-600",children:b.jsx(Xt,{className:"h-6 w-6"})}),b.jsx("button",{onClick:n,className:"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200 text-secondary-600 hover:text-primary-600",children:b.jsx(Qt,{className:"h-6 w-6"})}),b.jsx("div",{className:"flex justify-center mt-8 space-x-2",children:t.map((a,l)=>b.jsx("button",{onClick:()=>r(l),className:`w-3 h-3 rounded-full transition-colors duration-200 ${l===e?"bg-primary-600":"bg-secondary-300 hover:bg-secondary-400"}`},l))})]})}function ur({services:t=[],articles:e=[],testimonials:i=[],settings:s={}}){const n={ScaleIcon:be,DocumentTextIcon:st,UserGroupIcon:it,ShieldCheckIcon:tt};return t.map(r=>({...r,icon:n[r.icon]||be})),b.jsxs(Nt,{title:"Professional Legal Services in Metro Manila",children:[b.jsxs("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[b.jsxs("div",{className:"absolute inset-0 z-0",children:[b.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-secondary-900/90 to-primary-900/80 z-10"}),b.jsx("div",{className:"w-full h-full bg-cover bg-center bg-no-repeat",style:{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23334155" width="1200" height="800"/><g fill="%23475569"><rect x="100" y="200" width="80" height="120" rx="4"/><rect x="200" y="180" width="80" height="140" rx="4"/><rect x="300" y="220" width="80" height="100" rx="4"/><rect x="500" y="300" width="200" height="20" rx="10"/><rect x="500" y="340" width="150" height="20" rx="10"/><rect x="500" y="380" width="180" height="20" rx="10"/><circle cx="900" cy="250" r="60" fill="%23374151"/><rect x="850" y="320" width="100" height="8" rx="4"/><rect x="850" y="340" width="80" height="8" rx="4"/></g></svg>')`}})]}),b.jsxs("div",{className:"relative bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('/images/bg.png')"},children:[b.jsx("div",{className:"absolute inset-0 bg-black/30 z-10"}),b.jsx("div",{className:"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white py-24",children:b.jsxs(q.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[b.jsxs("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6",children:["Professional Legal Services",b.jsx("span",{className:"block text-primary-200",children:"You Can Trust"})]}),b.jsx("p",{className:"text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto",children:"Protecting your rights with integrity and professionalism."}),b.jsxs(q.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.3},children:[b.jsxs(pe,{href:"/contact",className:"bg-white text-primary-600 hover:bg-primary-50 hover:scale-105 font-semibold py-3 px-8 rounded-lg transition-all duration-200 inline-flex items-center justify-center shadow-lg",children:["For Consultation",b.jsx($e,{className:"ml-2 h-5 w-5"})]}),b.jsx(pe,{href:"/services",className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 hover:scale-105 font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-lg",children:"View Services"})]})]})})]}),b.jsx(q.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:b.jsxs("div",{className:"flex flex-col items-center",children:[b.jsx("span",{className:"text-sm mb-2",children:"Scroll Down"}),b.jsx("div",{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center",children:b.jsx("div",{className:"w-1 h-3 bg-white rounded-full mt-2"})})]})})]}),b.jsx(Bs,{services:t}),b.jsx(Dt,{articles:e}),b.jsx("section",{className:"py-20 bg-primary-600 text-white",children:b.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[b.jsxs(q.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[b.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-4",children:"Trusted by Many"}),b.jsx("p",{className:"text-xl text-primary-100 max-w-2xl mx-auto",children:"Years of experience serving clients across Metro Manila"})]}),b.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[b.jsx("div",{className:"text-center",children:b.jsx(xe,{end:15,suffix:"+",title:"Years Experience",description:"Serving clients with dedication"})}),b.jsx("div",{className:"text-center",children:b.jsx(xe,{end:500,suffix:"+",title:"Happy Clients",description:"Satisfied with our services"})}),b.jsx("div",{className:"text-center",children:b.jsx(xe,{end:1200,suffix:"+",title:"Documents Notarized",description:"Professional notarial services"})}),b.jsx("div",{className:"text-center",children:b.jsx(xe,{end:98,suffix:"%",title:"Success Rate",description:"In legal consultations"})})]})]})}),b.jsx("section",{className:"py-20 bg-secondary-50",children:b.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:b.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[b.jsxs("div",{children:[b.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold text-secondary-900 mb-6",children:"Why Choose BelgicaLaw?"}),b.jsx("div",{className:"space-y-4",children:["Experienced legal professionals","Personalized attention to each case","Transparent and fair pricing","Quick response and reliable service","Comprehensive legal solutions"].map((r,a)=>b.jsxs("div",{className:"flex items-center",children:[b.jsx(Ft,{className:"h-6 w-6 text-primary-600 mr-3 flex-shrink-0"}),b.jsx("span",{className:"text-secondary-700",children:r})]},a))})]}),b.jsxs("div",{className:"bg-white p-8 rounded-lg shadow-lg",children:[b.jsx("h3",{className:"text-xl font-semibold text-secondary-900 mb-4",children:"Ready to Get Started?"}),b.jsx("p",{className:"text-secondary-600 mb-6",children:"Contact us today for a free consultation and let us help you with your legal needs."}),b.jsx(pe,{href:"/contact",className:"btn-primary w-full text-center block",children:"Schedule Consultation"})]})]})})}),b.jsx("section",{className:"py-20 bg-white",children:b.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[b.jsxs(q.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[b.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold text-secondary-900 mb-4",children:"What Our Clients Say"}),b.jsx("p",{className:"text-lg text-secondary-600",children:"Hear from those who have trusted us with their legal needs"})]}),b.jsx(q.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:b.jsx(Us,{testimonials:i})})]})})]})}export{ur as default};
