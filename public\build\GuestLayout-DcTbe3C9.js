import{j as s,t}from"./app2.js";import{A as r}from"./ApplicationLogo-Bm4MQosc.js";function a({children:e}){return s.jsxs("div",{className:"flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0",children:[s.jsx("div",{children:s.jsx(t,{href:"/",children:s.jsx(r,{className:"h-20 w-20 fill-current text-gray-500"})})}),s.jsx("div",{className:"mt-6 w-full overflow-hidden bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg",children:e})]})}export{a as G};
