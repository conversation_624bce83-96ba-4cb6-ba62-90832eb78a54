import{j as s,Q as m}from"./app2.js";import{A as r}from"./AuthenticatedLayout-Czw7TcSH.js";import t from"./DeleteUserForm-DUuVomli.js";import i from"./UpdatePasswordForm-aLbRw2Et.js";import o from"./UpdateProfileInformationForm-5gZjGdEI.js";import"./ApplicationLogo-Bm4MQosc.js";import"./transition-BIFU3Srf.js";import"./TextInput-C5l2hSgh.js";import"./InputLabel-Hw4G8tDa.js";import"./PrimaryButton-DWJnXse-.js";function g({mustVerifyEmail:e,status:a}){return s.jsxs(r,{header:s.jsx("h2",{className:"text-xl font-semibold leading-tight text-gray-800",children:"Profile"}),children:[s.jsx(m,{title:"Profile"}),s.jsx("div",{className:"py-12",children:s.jsxs("div",{className:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8",children:[s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(o,{mustVerifyEmail:e,status:a,className:"max-w-xl"})}),s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(i,{className:"max-w-xl"})}),s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(t,{className:"max-w-xl"})})]})})]})}export{g as default};
