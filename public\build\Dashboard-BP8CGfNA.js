import{j as e,Q as s}from"./app2.js";import{A as a}from"./AuthenticatedLayout-Czw7TcSH.js";import"./ApplicationLogo-Bm4MQosc.js";import"./transition-BIFU3Srf.js";function o(){return e.jsxs(a,{header:e.jsx("h2",{className:"text-xl font-semibold leading-tight text-gray-800",children:"Dashboard"}),children:[e.jsx(s,{title:"Dashboard"}),e.jsx("div",{className:"py-12",children:e.jsx("div",{className:"mx-auto max-w-7xl sm:px-6 lg:px-8",children:e.jsx("div",{className:"overflow-hidden bg-white shadow-sm sm:rounded-lg",children:e.jsx("div",{className:"p-6 text-gray-900",children:"You're logged in!"})})})})]})}export{o as default};
