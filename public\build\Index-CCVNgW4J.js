import{j as e,Q as h,t as l,S as g}from"./app2.js";import{A as j,a as n}from"./AdminLayout-CA9t8iTo.js";import{F as m}from"./PlusIcon-CFTIzR_P.js";import{F as u}from"./CheckCircleIcon-C2jnGy7x.js";import{F as f}from"./XCircleIcon-Bp7te63t.js";import{F as p}from"./EyeIcon-CuARlRRc.js";import{F as N}from"./PencilIcon-BlseKOW2.js";import{F as b}from"./TrashIcon-CkX595jR.js";import"./HomeIcon-BtCkMBaV.js";import"./XMarkIcon-DvlfLOeu.js";function k({auth:x,faqs:i,flash:a}){const c=s=>{confirm(`Are you sure you want to delete "${s.question}"?`)&&g.delete(`/admin/faqs/${s.id}`)},o=s=>({services:"bg-blue-100 text-blue-800",pricing:"bg-green-100 text-green-800",consultation:"bg-purple-100 text-purple-800",general:"bg-gray-100 text-gray-800",legal:"bg-red-100 text-red-800",process:"bg-yellow-100 text-yellow-800"})[s]||"bg-gray-100 text-gray-800",d=i.reduce((s,r)=>{const t=r.category;return s[t]||(s[t]=[]),s[t].push(r),s},{});return e.jsxs(j,{user:x.user,children:[e.jsx(h,{title:"Manage FAQs"}),e.jsx("div",{className:"py-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"md:flex md:items-center md:justify-between mb-6",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Manage FAQs"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Create and manage frequently asked questions"})]}),e.jsx("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:e.jsxs(l,{href:"/admin/faqs/create",className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(m,{className:"-ml-1 mr-2 h-5 w-5"}),"Add FAQ"]})})]}),(a==null?void 0:a.success)&&e.jsx("div",{className:"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:a.success}),Object.keys(d).length>0?e.jsx("div",{className:"space-y-8",children:Object.entries(d).map(([s,r])=>e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 capitalize",children:[s," (",r.length,")"]}),e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${o(s)}`,children:s})]})}),e.jsx("div",{className:"divide-y divide-gray-200",children:r.map(t=>e.jsx("div",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(n,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0"}),e.jsx("h4",{className:"text-sm font-medium text-gray-900 line-clamp-2",children:t.question}),e.jsx("div",{className:"ml-2 flex items-center",children:t.is_published?e.jsx(u,{className:"h-4 w-4 text-green-500",title:"Published"}):e.jsx(f,{className:"h-4 w-4 text-red-500",title:"Draft"})})]}),e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2 mb-2",children:t.answer}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsxs("span",{children:["Sort Order: ",t.sort_order]}),e.jsx("span",{className:"mx-2",children:"•"}),e.jsx("span",{children:t.is_published?"Published":"Draft"}),e.jsx("span",{className:"mx-2",children:"•"}),e.jsxs("span",{children:["Updated ",new Date(t.updated_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"ml-4 flex items-center space-x-2",children:[e.jsxs(l,{href:`/admin/faqs/${t.id}`,className:"text-indigo-600 hover:text-indigo-900 text-sm font-medium flex items-center",children:[e.jsx(p,{className:"h-4 w-4 mr-1"}),"View"]}),e.jsxs(l,{href:`/admin/faqs/${t.id}/edit`,className:"text-indigo-600 hover:text-indigo-900 text-sm font-medium flex items-center",children:[e.jsx(N,{className:"h-4 w-4 mr-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>c(t),className:"text-red-600 hover:text-red-900 text-sm font-medium flex items-center",children:[e.jsx(b,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})},t.id))})]},s))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(n,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No FAQs yet"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating your first FAQ."}),e.jsx("div",{className:"mt-6",children:e.jsxs(l,{href:"/admin/faqs/create",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[e.jsx(m,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Your First FAQ"]})})]}),i.length>0&&e.jsxs("div",{className:"mt-8 bg-gray-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"FAQ Statistics"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-indigo-600",children:i.length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Total FAQs"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:i.filter(s=>s.is_published).length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Published"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:i.filter(s=>!s.is_published).length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Drafts"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:Object.keys(d).length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Categories"})]})]})]})]})})]})}export{k as default};
